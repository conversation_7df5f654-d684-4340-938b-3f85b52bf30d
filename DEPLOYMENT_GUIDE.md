# ViserBus 自动部署指南

## 🚀 一键自动部署

我们为您准备了多个自动化部署脚本，根据您的服务器环境选择合适的脚本：

### 📜 可用的部署脚本

1. **deploy-ubuntu.sh** - Ubuntu/Debian 服务器
2. **deploy-centos.sh** - CentOS/RHEL 服务器
3. **deploy-cpanel.sh** - cPanel 共享主机
4. **migrate-database.sh** - 数据库迁移工具

## 🎯 快速部署步骤

### 方法1: Ubuntu/Debian 服务器

```bash
# 1. 上传项目文件到服务器
scp -r Files/ user@your-server:/tmp/

# 2. 连接到服务器
ssh user@your-server

# 3. 编辑配置
cd /tmp/Files
nano deploy-ubuntu.sh
# 修改以下变量:
# DOMAIN="yourdomain.com"
# DB_NAME="viserbus_db"
# DB_USER="viserbus_user"
# DB_PASS="your_secure_password"
# ADMIN_EMAIL="<EMAIL>"

# 4. 运行部署脚本
chmod +x deploy-ubuntu.sh
sudo ./deploy-ubuntu.sh
```

### 方法2: CentOS/RHEL 服务器

```bash
# 1. 上传项目文件到服务器
scp -r Files/ user@your-server:/tmp/

# 2. 连接到服务器
ssh user@your-server

# 3. 编辑配置
cd /tmp/Files
nano deploy-centos.sh
# 修改配置变量

# 4. 运行部署脚本
chmod +x deploy-centos.sh
sudo ./deploy-centos.sh
```

### 方法3: cPanel 共享主机

```bash
# 1. 通过 cPanel 文件管理器上传 Files 文件夹

# 2. 通过 cPanel 终端或 SSH 连接

# 3. 编辑配置
cd ~/Files  # 或您上传的目录
nano deploy-cpanel.sh
# 修改配置变量

# 4. 运行部署脚本
bash deploy-cpanel.sh
```

## ⚙️ 配置说明

### 必须修改的配置项

在运行部署脚本前，请编辑脚本中的以下变量：

```bash
DOMAIN="yourdomain.com"              # 您的域名
PROJECT_PATH="/var/www/html"         # 项目路径 (VPS)
DB_NAME="viserbus_db"               # 数据库名
DB_USER="viserbus_user"             # 数据库用户
DB_PASS="your_secure_password"      # 数据库密码
ADMIN_EMAIL="<EMAIL>"  # 管理员邮箱
```

## 🔄 数据库迁移 (可选)

如果您想从 SQLite 迁移到 MySQL：

```bash
# 编辑迁移脚本配置
nano migrate-database.sh

# 运行迁移
bash migrate-database.sh
```

## 📋 部署前准备

### 系统要求
- PHP 8.1 或更高版本
- MySQL 5.7+ 或 MariaDB 10.3+ (推荐) 或 SQLite 3.8+
- Apache/Nginx Web服务器
- Composer (PHP包管理器)
- SSL证书 (推荐)

### 必需的PHP扩展
- BCMath
- Ctype
- Fileinfo
- JSON
- Mbstring
- OpenSSL
- PDO
- Tokenizer
- XML
- GD或Imagick (图片处理)

## 🚀 部署步骤

### 1. 上传文件
将整个 `Files` 文件夹上传到您的服务器。建议结构：
```
/public_html/
├── assets/          (静态资源)
├── core/           (Laravel应用核心)
└── index.php       (入口文件)
```

### 2. 设置Web服务器文档根目录
将您的域名指向 `/public_html` 目录，而不是 `/public_html/core/public`

### 3. 配置环境文件
```bash
cd /path/to/your/core
cp .env.production .env
```

编辑 `.env` 文件，更新以下配置：

#### 基本配置
```env
APP_NAME="您的网站名称"
APP_URL=https://yourdomain.com
APP_DEBUG=false
APP_ENV=production
```

#### 数据库配置 (MySQL推荐)
```env
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

#### 或者使用SQLite (简单部署)
```env
DB_CONNECTION=sqlite
DB_DATABASE=/absolute/path/to/database/database.sqlite
```

#### 邮件配置
```env
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
```

### 4. 设置文件权限
```bash
chmod -R 755 /path/to/your/core
chmod -R 775 /path/to/your/core/storage
chmod -R 775 /path/to/your/core/bootstrap/cache
chmod -R 775 /path/to/your/assets
```

### 5. 安装依赖 (如果需要)
```bash
cd /path/to/your/core
composer install --optimize-autoloader --no-dev
```

### 6. 数据库迁移 (如果使用MySQL)
如果您选择使用MySQL而不是SQLite：

1. 创建数据库
2. 导入数据：
```bash
# 从SQLite导出数据 (在本地)
php artisan db:seed --class=ProductionDataSeeder

# 或手动导入现有数据
```

### 7. 优化应用
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🔧 Web服务器配置

### Apache (.htaccess)
确保根目录有正确的 `.htaccess` 文件：
```apache
RewriteEngine On
RewriteRule ^(.*)$ core/public/$1 [L]
```

### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/your/public_html;
    index index.php;

    location / {
        try_files $uri $uri/ /core/public/index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 📁 重要文件和文件夹

### 必须保留的文件
- `core/` - Laravel应用核心
- `assets/` - 静态资源文件
- `index.php` - 主入口文件
- `core/database/database.sqlite` - 数据库文件 (包含所有页面数据)
- `core/.env` - 环境配置文件

### 可以删除的文件 (已清理)
- 所有测试文件
- 开发工具文件
- 调试文件
- 临时文件

## 🔒 安全建议

1. **SSL证书**: 强烈建议使用HTTPS
2. **文件权限**: 确保敏感文件不可直接访问
3. **环境文件**: 确保 `.env` 文件不可通过Web访问
4. **定期备份**: 备份数据库和上传的文件
5. **更新**: 定期检查安全更新

## 🎯 部署后检查

1. 访问您的网站首页
2. 访问管理后台: `https://yourdomain.com/admin`
3. 检查所有页面是否正常加载
4. 测试联系表单和邮件功能
5. 检查图片和静态资源是否正常显示

## 🆘 常见问题

### 500错误
- 检查文件权限
- 检查 `.env` 配置
- 查看错误日志: `core/storage/logs/laravel.log`

### 页面不显示
- 检查Web服务器配置
- 确认文档根目录设置正确

### 数据库连接错误
- 验证数据库配置
- 确认数据库服务器运行正常

## 📞 技术支持

如果遇到部署问题，请检查：
1. PHP版本和扩展
2. Web服务器配置
3. 文件权限
4. 数据库连接
5. 错误日志

---
**注意**: 此应用已经过完整测试，包含所有自定义页面数据。部署时请确保保留 `database.sqlite` 文件以保持所有页面内容。
