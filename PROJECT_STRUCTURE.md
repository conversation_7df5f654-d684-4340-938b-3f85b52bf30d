# ViserBus 项目结构说明

## 📁 文件夹结构

```
Files/
├── assets/                     # 静态资源文件
│   ├── admin/                 # 管理后台静态资源
│   ├── errors/                # 错误页面资源
│   ├── font/                  # 字体文件
│   ├── global/                # 全局资源
│   ├── images/                # 图片资源
│   ├── support/               # 支持文件
│   ├── templates/             # 模板资源
│   └── verify/                # 验证相关资源
├── core/                      # Laravel 应用核心
│   ├── app/                   # 应用代码
│   │   ├── Constants/         # 常量定义
│   │   ├── Http/              # HTTP 相关
│   │   │   ├── Controllers/   # 控制器
│   │   │   ├── Helpers/       # 助手函数
│   │   │   └── Middleware/    # 中间件
│   │   ├── Lib/               # 库文件
│   │   ├── Models/            # 数据模型
│   │   ├── Notify/            # 通知系统
│   │   ├── Providers/         # 服务提供者
│   │   ├── Rules/             # 验证规则
│   │   ├── Traits/            # 特性
│   │   └── View/              # 视图组件
│   ├── bootstrap/             # 启动文件
│   ├── config/                # 配置文件
│   ├── database/              # 数据库
│   │   ├── database.sqlite    # SQLite 数据库文件 (包含所有页面数据)
│   │   ├── migrations/        # 数据库迁移
│   │   └── seeders/           # 数据填充
│   ├── public/                # 公共文件
│   │   ├── assets/            # 公共静态资源
│   │   └── index.php          # Laravel 入口文件
│   ├── resources/             # 资源文件
│   │   ├── lang/              # 语言文件
│   │   └── views/             # 视图文件
│   ├── routes/                # 路由文件
│   ├── storage/               # 存储文件
│   │   ├── app/               # 应用存储
│   │   ├── framework/         # 框架缓存
│   │   └── logs/              # 日志文件
│   ├── vendor/                # Composer 依赖
│   ├── .env                   # 环境配置文件
│   ├── .env.production        # 生产环境配置示例
│   ├── artisan                # Artisan 命令行工具
│   ├── composer.json          # Composer 配置
│   └── composer.lock          # Composer 锁定文件
├── install/                   # 安装文件 (可选)
├── .htaccess                  # Apache 重写规则
├── index.php                  # 主入口文件
├── DEPLOYMENT_GUIDE.md        # 部署指南
└── PROJECT_STRUCTURE.md       # 项目结构说明 (本文件)
```

## 🗄️ 重要数据文件

### 数据库文件
- **位置**: `core/database/database.sqlite`
- **内容**: 包含所有页面数据、用户数据、设置等
- **重要性**: ⚠️ 极其重要 - 包含所有自定义内容

### 配置文件
- **开发环境**: `core/.env`
- **生产环境示例**: `core/.env.production`

### 上传文件存储
- **位置**: `core/storage/app/public/`
- **内容**: 用户上传的图片和文件

## 🎯 核心功能模块

### 前端模板系统
- **模板文件**: `core/resources/views/templates/basic/`
- **静态资源**: `assets/templates/basic/`
- **配置文件**: `core/resources/views/templates/basic/sections.json`

### 管理后台
- **控制器**: `core/app/Http/Controllers/Admin/`
- **视图**: `core/resources/views/admin/`
- **静态资源**: `assets/admin/`

### 数据模型
- **页面管理**: `core/app/Models/Page.php`
- **前端内容**: `core/app/Models/Frontend.php`
- **用户管理**: `core/app/Models/User.php`
- **管理员**: `core/app/Models/Admin.php`

## 🔧 已优化的功能

### 性能优化
- ✅ 配置缓存已生成
- ✅ 路由缓存已生成
- ✅ 视图缓存已生成
- ✅ 重复路由问题已修复

### 错误修复
- ✅ foreach 类型错误已修复
- ✅ JSON 解码错误处理已添加
- ✅ 数据库 slug 问题已修复
- ✅ 页面构建器错误已修复

### 安全增强
- ✅ 生产环境配置已准备
- ✅ 调试模式已关闭 (生产环境)
- ✅ 敏感文件访问已限制

## 📊 数据统计

当前数据库包含：
- **页面数量**: 3 个 (Home, About, Contact 等)
- **前端内容**: 21 个内容块
- **系统设置**: 1 套完整配置

## 🚀 部署就绪

项目已完全准备好部署到生产环境：

1. **文件已清理**: 删除了所有测试和开发文件
2. **配置已优化**: 生产环境配置文件已准备
3. **缓存已生成**: 所有必要的缓存已预生成
4. **错误已修复**: 所有已知错误已解决
5. **数据已保留**: 所有自定义页面和设置已保留

## ⚠️ 部署注意事项

1. **保护数据库文件**: 确保 `database.sqlite` 文件安全
2. **设置正确权限**: 确保 storage 和 bootstrap/cache 文件夹可写
3. **配置环境文件**: 根据生产环境更新 `.env` 文件
4. **SSL 证书**: 强烈建议使用 HTTPS
5. **定期备份**: 定期备份数据库和上传文件

---

**项目状态**: ✅ 生产就绪
**最后更新**: 2025-09-17
**版本**: ViserBus Custom Template v1.0
