#!/bin/bash

# ViserBus Laravel 项目自动安装脚本
# 适用于 cPanel 环境
# 作者: AI Assistant
# 版本: 1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 配置变量
PROJECT_NAME="superiortour.com.sg"
CORE_DIR="core"
INSTALL_DIR="install"

print_header "ViserBus Laravel 自动安装脚本"
echo -e "${YELLOW}此脚本将自动安装和配置 ViserBus Laravel 项目${NC}"
echo ""

# 步骤 1: 检查环境
print_step "Step 1: 检查安装环境"

# 检查是否在正确目录
if [ ! -d "$PROJECT_NAME" ]; then
    print_error "未找到项目目录: $PROJECT_NAME"
    print_warning "请确保您在包含项目文件的目录中运行此脚本"
    exit 1
fi

cd "$PROJECT_NAME"
print_success "找到项目目录: $(pwd)"

# 检查核心文件
if [ ! -d "$CORE_DIR" ]; then
    print_error "未找到核心目录: $CORE_DIR"
    exit 1
fi

if [ ! -f "$CORE_DIR/artisan" ]; then
    print_error "未找到 Laravel artisan 文件"
    exit 1
fi

print_success "Laravel 项目结构验证通过"

# 检查 PHP 版本
PHP_VERSION=$(php -r "echo PHP_VERSION;")
print_success "PHP 版本: $PHP_VERSION"

# 检查必要的 PHP 扩展
print_step "检查 PHP 扩展"
REQUIRED_EXTENSIONS=("pdo" "pdo_sqlite" "pdo_mysql" "mbstring" "curl" "json" "zip" "gd" "xml")
MISSING_EXTENSIONS=()

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "^$ext$"; then
        echo -e "  ${GREEN}✓${NC} $ext"
    else
        echo -e "  ${RED}✗${NC} $ext"
        MISSING_EXTENSIONS+=("$ext")
    fi
done

if [ ${#MISSING_EXTENSIONS[@]} -gt 0 ]; then
    print_error "缺少必要的 PHP 扩展: ${MISSING_EXTENSIONS[*]}"
    print_warning "请联系主机提供商安装这些扩展"
    exit 1
fi

print_success "所有必要的 PHP 扩展已安装"
echo ""

# 步骤 2: 设置项目
print_step "Step 2: 设置 Laravel 项目"

cd "$CORE_DIR"

# 检查 .env 文件
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_success "已创建 .env 文件"
    else
        print_error "未找到 .env.example 文件"
        exit 1
    fi
else
    print_success ".env 文件已存在"
fi

# 生成应用密钥
if ! grep -q "APP_KEY=base64:" .env; then
    print_step "生成应用密钥"
    php artisan key:generate --force
    print_success "应用密钥已生成"
else
    print_success "应用密钥已存在"
fi

# 创建 SQLite 数据库文件
print_step "创建数据库文件"
if [ ! -f "database/database.sqlite" ]; then
    touch database/database.sqlite
    chmod 664 database/database.sqlite
    print_success "SQLite 数据库文件已创建"
else
    print_success "数据库文件已存在"
fi

# 步骤 3: 安装依赖
print_step "Step 3: 安装 Composer 依赖"

if [ -f "composer.json" ]; then
    if command -v composer &> /dev/null; then
        composer install --no-dev --optimize-autoloader
        print_success "Composer 依赖安装完成"
    else
        print_warning "Composer 未安装，跳过依赖安装"
        print_warning "请手动运行: composer install --no-dev --optimize-autoloader"
    fi
else
    print_warning "未找到 composer.json 文件"
fi

echo ""

# 步骤 4: 数据库初始化
print_step "Step 4: 初始化数据库"

# 使用安装目录中的 SQL 文件
if [ -f "../$INSTALL_DIR/database.sql" ]; then
    print_step "导入完整数据库结构"
    
    # 转换 MySQL SQL 到 SQLite
    print_step "转换数据库格式 (MySQL → SQLite)"
    
    # 创建临时 SQLite 兼容的 SQL 文件
    cat > temp_sqlite.sql << 'EOF'
PRAGMA foreign_keys = OFF;
BEGIN TRANSACTION;
EOF

    # 处理 SQL 文件，转换为 SQLite 格式
    sed -e 's/ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;//g' \
        -e 's/CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci//g' \
        -e 's/bigint UNSIGNED/INTEGER/g' \
        -e 's/bigint/INTEGER/g' \
        -e 's/int UNSIGNED/INTEGER/g' \
        -e 's/tinyint(1)/INTEGER/g' \
        -e 's/varchar([0-9]*)/TEXT/g' \
        -e 's/text/TEXT/g' \
        -e 's/longtext/TEXT/g' \
        -e 's/datetime/TEXT/g' \
        -e 's/timestamp/TEXT/g' \
        -e 's/decimal([0-9,]*)/REAL/g' \
        -e 's/AUTO_INCREMENT/AUTOINCREMENT/g' \
        -e 's/NOT NULL AUTO_INCREMENT/INTEGER PRIMARY KEY AUTOINCREMENT/g' \
        -e '/^SET /d' \
        -e '/^START TRANSACTION/d' \
        -e '/^COMMIT/d' \
        -e '/^\/\*!/d' \
        -e '/^--$/d' \
        -e '/^-- --------------------------------------------------------$/d' \
        -e '/^-- Table structure for table/d' \
        -e '/^-- Dumping data for table/d' \
        -e '/^-- Indexes for table/d' \
        -e '/^-- AUTO_INCREMENT for table/d' \
        -e '/^-- Constraints for table/d' \
        -e '/^ALTER TABLE.*ADD PRIMARY KEY/d' \
        -e '/^ALTER TABLE.*MODIFY.*AUTO_INCREMENT/d' \
        -e '/^ALTER TABLE.*ADD CONSTRAINT/d' \
        "../$INSTALL_DIR/database.sql" >> temp_sqlite.sql

    echo "COMMIT;" >> temp_sqlite.sql
    
    # 导入到 SQLite
    sqlite3 database/database.sqlite < temp_sqlite.sql
    
    # 清理临时文件
    rm temp_sqlite.sql
    
    print_success "数据库结构和数据导入完成"
else
    print_warning "未找到安装 SQL 文件，使用 Laravel 迁移"
    
    # 运行迁移
    php artisan migrate --force
    print_success "数据库迁移完成"
fi

echo ""

# 步骤 5: 设置权限
print_step "Step 5: 设置文件权限"

# 设置存储目录权限
if [ -d "storage" ]; then
    chmod -R 775 storage
    print_success "storage 目录权限已设置"
fi

# 设置缓存目录权限
if [ -d "bootstrap/cache" ]; then
    chmod -R 775 bootstrap/cache
    print_success "bootstrap/cache 目录权限已设置"
fi

# 设置数据库文件权限
if [ -f "database/database.sqlite" ]; then
    chmod 664 database/database.sqlite
    print_success "数据库文件权限已设置"
fi

echo ""

# 步骤 6: 优化和缓存
print_step "Step 6: 优化应用"

# 清除所有缓存
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# 生成优化缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache

print_success "应用缓存已优化"

echo ""

# 步骤 7: 验证安装
print_step "Step 7: 验证安装"

# 测试数据库连接
if php artisan tinker --execute="echo 'Database connection: '; try { \$pdo = DB::connection()->getPdo(); echo 'SUCCESS'; } catch(Exception \$e) { echo 'FAILED: ' . \$e->getMessage(); }" 2>/dev/null | grep -q "SUCCESS"; then
    print_success "数据库连接测试通过"
else
    print_error "数据库连接测试失败"
fi

# 检查关键表
print_step "检查数据库表"
TABLES=("users" "general_settings" "admins" "frontends")
for table in "${TABLES[@]}"; do
    if sqlite3 database/database.sqlite "SELECT name FROM sqlite_master WHERE type='table' AND name='$table';" | grep -q "$table"; then
        COUNT=$(sqlite3 database/database.sqlite "SELECT COUNT(*) FROM $table;")
        print_success "$table 表存在 ($COUNT 条记录)"
    else
        print_warning "$table 表不存在"
    fi
done

echo ""

# 完成安装
print_header "安装完成！"
echo ""
print_success "ViserBus Laravel 项目安装成功！"
echo ""
echo -e "${YELLOW}下一步操作:${NC}"
echo "1. 访问您的网站查看前端"
echo "2. 访问 /admin 进入管理后台"
echo "3. 默认管理员账户:"
echo "   - 用户名: admin"
echo "   - 密码: password"
echo ""
echo -e "${YELLOW}重要提醒:${NC}"
echo "- 请立即更改默认管理员密码"
echo "- 检查 .env 文件中的配置"
echo "- 确保文件权限设置正确"
echo ""
print_success "安装脚本执行完毕！"
