#!/bin/bash

# ViserBus 旧网站安全清理脚本
# 使用方法: bash cleanup-old-site.sh

set -e  # 遇到错误立即退出

echo "🧹 开始清理旧网站文件..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BACKUP_DIR="$HOME/old_site_backup_$(date +%Y%m%d_%H%M%S)"
PUBLIC_HTML="$HOME/public_html"

echo -e "${BLUE}📋 清理配置:${NC}"
echo -e "网站目录: ${GREEN}$PUBLIC_HTML${NC}"
echo -e "备份目录: ${GREEN}$BACKUP_DIR${NC}"

# 检查是否在正确目录
if [ ! -d "$PUBLIC_HTML" ]; then
    echo -e "${RED}❌ 找不到 public_html 目录${NC}"
    exit 1
fi

# 1. 创建备份目录
echo -e "${YELLOW}💾 创建备份目录...${NC}"
mkdir -p "$BACKUP_DIR"

# 2. 备份重要文件
echo -e "${YELLOW}📦 备份重要文件...${NC}"

cd "$PUBLIC_HTML"

# 备份用户上传的文件
if [ -d "uploads" ]; then
    echo -e "备份 uploads 文件夹..."
    cp -r uploads "$BACKUP_DIR/"
fi

# 备份自定义图片
if [ -d "assets/images" ]; then
    echo -e "备份 assets/images 文件夹..."
    mkdir -p "$BACKUP_DIR/assets"
    cp -r assets/images "$BACKUP_DIR/assets/"
fi

# 备份配置文件
if [ -f ".env" ]; then
    echo -e "备份 .env 配置文件..."
    cp .env "$BACKUP_DIR/"
fi

if [ -f "core/.env" ]; then
    echo -e "备份 core/.env 配置文件..."
    cp core/.env "$BACKUP_DIR/"
fi

# 备份数据库文件 (如果使用 SQLite)
if [ -f "core/database/database.sqlite" ]; then
    echo -e "备份 SQLite 数据库..."
    mkdir -p "$BACKUP_DIR/database"
    cp core/database/database.sqlite "$BACKUP_DIR/database/"
fi

# 备份自定义文件
if [ -f "robots.txt" ]; then
    cp robots.txt "$BACKUP_DIR/"
fi

if [ -f "sitemap.xml" ]; then
    cp sitemap.xml "$BACKUP_DIR/"
fi

# 3. 列出将要删除的文件
echo -e "${YELLOW}📋 将要删除的文件/文件夹:${NC}"
echo -e "${RED}以下文件将被删除:${NC}"

# 要删除的文件和文件夹列表
DELETE_ITEMS=(
    "core"
    "assets/admin"
    "assets/errors" 
    "assets/font"
    "assets/global"
    "assets/support"
    "assets/templates"
    "assets/verify"
    "install"
    "updates"
    "htaccess"
    "index.php"
    "Bus Booking System.zip"
    "index.php"
)

for item in "${DELETE_ITEMS[@]}"; do
    if [ -e "$item" ]; then
        echo -e "  ${RED}✗${NC} $item"
    fi
done

echo -e "${GREEN}以下文件将保留:${NC}"
KEEP_ITEMS=(
    "uploads"
    "assets/images"
    ".env"
    "robots.txt"
    "sitemap.xml"
)

for item in "${KEEP_ITEMS[@]}"; do
    if [ -e "$item" ]; then
        echo -e "  ${GREEN}✓${NC} $item"
    fi
done

# 4. 确认删除
echo -e "${YELLOW}⚠️  确认删除操作${NC}"
echo -e "${RED}此操作将删除旧网站文件！${NC}"
echo -e "备份已保存到: ${GREEN}$BACKUP_DIR${NC}"
echo ""
read -p "确认继续删除？(输入 'YES' 确认): " confirm

if [ "$confirm" != "YES" ]; then
    echo -e "${BLUE}❌ 操作已取消${NC}"
    exit 0
fi

# 5. 执行删除
echo -e "${YELLOW}🗑️  开始删除旧文件...${NC}"

for item in "${DELETE_ITEMS[@]}"; do
    if [ -e "$item" ]; then
        echo -e "删除: $item"
        rm -rf "$item"
    fi
done

# 删除其他常见的旧文件
find . -name "*.log" -type f -delete 2>/dev/null || true
find . -name "*.tmp" -type f -delete 2>/dev/null || true
find . -name ".DS_Store" -type f -delete 2>/dev/null || true

# 6. 清理完成
echo -e "${GREEN}✅ 清理完成!${NC}"
echo -e "${BLUE}📊 清理摘要:${NC}"
echo -e "备份位置: ${GREEN}$BACKUP_DIR${NC}"
echo -e "剩余文件:"

ls -la "$PUBLIC_HTML"

echo -e "${YELLOW}📝 下一步:${NC}"
echo -e "1. 上传新的 ViserBus 文件"
echo -e "2. 运行部署脚本"
echo -e "3. 如果需要，从备份中恢复用户数据"

echo -e "${GREEN}🎉 旧网站清理成功!${NC}"
