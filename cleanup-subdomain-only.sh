#!/bin/bash

# ViserBus 子域名安全清理脚本
# 专门清理 superiortour.com.my，保护主域名 sgmy.taxi
# 使用方法: bash cleanup-subdomain-only.sh

set -e  # 遇到错误立即退出

echo "🧹 开始安全清理子域名 superiortour.com.my..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
MAIN_DOMAIN="sgmy.taxi"
SUB_DOMAIN="superiortour.com.my"
PUBLIC_HTML="$HOME/public_html"
BACKUP_DIR="$HOME/superiortour_backup_$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}📋 清理配置:${NC}"
echo -e "主域名 (保护): ${GREEN}$MAIN_DOMAIN${NC}"
echo -e "子域名 (清理): ${RED}$SUB_DOMAIN${NC}"
echo -e "网站根目录: ${GREEN}$PUBLIC_HTML${NC}"

# 1. 检测子域名目录结构
echo -e "${YELLOW}🔍 检测域名目录结构...${NC}"

SUBDOMAIN_PATH=""

# 可能的子域名路径
POSSIBLE_PATHS=(
    "$PUBLIC_HTML/$SUB_DOMAIN"
    "$PUBLIC_HTML/superiortour.com.my"
    "$PUBLIC_HTML/subdomains/superiortour"
    "$PUBLIC_HTML/addon_domains/superiortour.com.my"
)

for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -d "$path" ]; then
        SUBDOMAIN_PATH="$path"
        echo -e "找到子域名目录: ${GREEN}$path${NC}"
        break
    fi
done

if [ -z "$SUBDOMAIN_PATH" ]; then
    echo -e "${RED}❌ 未找到 $SUB_DOMAIN 的目录${NC}"
    echo -e "${YELLOW}请手动确认子域名目录位置${NC}"
    echo -e "${BLUE}可能的位置:${NC}"
    for path in "${POSSIBLE_PATHS[@]}"; do
        echo -e "  - $path"
    done
    exit 1
fi

# 2. 安全检查 - 确保不会影响主域名
echo -e "${YELLOW}🛡️  安全检查...${NC}"

# 检查是否在主域名目录内
if [[ "$SUBDOMAIN_PATH" == "$PUBLIC_HTML" ]]; then
    echo -e "${RED}❌ 危险！子域名路径指向主域名根目录${NC}"
    echo -e "${RED}这会删除主域名 $MAIN_DOMAIN 的所有文件！${NC}"
    exit 1
fi

# 检查主域名文件是否存在
MAIN_DOMAIN_FILES=("$PUBLIC_HTML/index.php" "$PUBLIC_HTML/index.html" "$PUBLIC_HTML/.htaccess")
MAIN_DOMAIN_EXISTS=false

for file in "${MAIN_DOMAIN_FILES[@]}"; do
    if [ -f "$file" ]; then
        MAIN_DOMAIN_EXISTS=true
        break
    fi
done

if [ "$MAIN_DOMAIN_EXISTS" = true ]; then
    echo -e "${GREEN}✅ 主域名 $MAIN_DOMAIN 文件检测正常${NC}"
else
    echo -e "${YELLOW}⚠️  主域名文件未检测到，请确认主域名状态${NC}"
fi

# 3. 显示将要清理的内容
echo -e "${YELLOW}📋 将要清理的目录:${NC}"
echo -e "${RED}$SUBDOMAIN_PATH${NC}"
echo ""
echo -e "${BLUE}目录内容:${NC}"
ls -la "$SUBDOMAIN_PATH" 2>/dev/null || echo "目录为空或无法访问"

# 4. 创建备份（以防万一）
echo -e "${YELLOW}💾 创建安全备份...${NC}"
mkdir -p "$BACKUP_DIR"

if [ -d "$SUBDOMAIN_PATH" ]; then
    echo -e "备份 $SUB_DOMAIN 到 $BACKUP_DIR"
    cp -r "$SUBDOMAIN_PATH" "$BACKUP_DIR/"
fi

# 5. 最终确认
echo -e "${RED}⚠️  最终确认${NC}"
echo -e "即将删除: ${RED}$SUBDOMAIN_PATH${NC}"
echo -e "主域名保护: ${GREEN}$MAIN_DOMAIN (不受影响)${NC}"
echo -e "备份位置: ${GREEN}$BACKUP_DIR${NC}"
echo ""
echo -e "${YELLOW}请仔细确认以上信息正确！${NC}"
read -p "确认删除 $SUB_DOMAIN 的所有文件？(输入 'DELETE-SUBDOMAIN' 确认): " confirm

if [ "$confirm" != "DELETE-SUBDOMAIN" ]; then
    echo -e "${BLUE}❌ 操作已取消${NC}"
    exit 0
fi

# 6. 执行清理
echo -e "${YELLOW}🗑️  开始清理 $SUB_DOMAIN...${NC}"

if [ -d "$SUBDOMAIN_PATH" ]; then
    echo -e "删除目录: $SUBDOMAIN_PATH"
    rm -rf "$SUBDOMAIN_PATH"/*
    echo -e "${GREEN}✅ 子域名目录已清空${NC}"
else
    echo -e "${YELLOW}⚠️  子域名目录不存在，可能已经清空${NC}"
fi

# 7. 重新创建干净的目录
echo -e "${YELLOW}📁 重新创建干净目录...${NC}"
mkdir -p "$SUBDOMAIN_PATH"
chmod 755 "$SUBDOMAIN_PATH"

# 8. 检查主域名是否受影响
echo -e "${YELLOW}🔍 检查主域名状态...${NC}"
MAIN_DOMAIN_AFFECTED=false

for file in "${MAIN_DOMAIN_FILES[@]}"; do
    if [ ! -f "$file" ] && [ "$MAIN_DOMAIN_EXISTS" = true ]; then
        MAIN_DOMAIN_AFFECTED=true
        break
    fi
done

if [ "$MAIN_DOMAIN_AFFECTED" = true ]; then
    echo -e "${RED}❌ 警告：主域名文件可能受到影响！${NC}"
    echo -e "${YELLOW}请立即检查主域名 $MAIN_DOMAIN${NC}"
else
    echo -e "${GREEN}✅ 主域名 $MAIN_DOMAIN 未受影响${NC}"
fi

# 9. 清理完成
echo -e "${GREEN}✅ 子域名清理完成!${NC}"
echo -e "${BLUE}📊 清理摘要:${NC}"
echo -e "已清理: ${RED}$SUB_DOMAIN${NC}"
echo -e "已保护: ${GREEN}$MAIN_DOMAIN${NC}"
echo -e "备份位置: ${GREEN}$BACKUP_DIR${NC}"
echo -e "子域名目录: ${GREEN}$SUBDOMAIN_PATH${NC}"

echo -e "${YELLOW}📝 下一步:${NC}"
echo -e "1. 上传新的 ViserBus 文件到: $SUBDOMAIN_PATH"
echo -e "2. 配置子域名的 .env 文件"
echo -e "3. 运行部署脚本"
echo -e "4. 测试 $SUB_DOMAIN 功能"

echo -e "${GREEN}🎉 安全清理成功!${NC}"
