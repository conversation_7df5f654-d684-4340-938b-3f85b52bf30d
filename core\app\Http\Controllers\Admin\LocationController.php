<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class LocationController extends Controller
{
    // Pickup Locations
    public function pickupLocations()
    {
        $pageTitle = 'Pickup Locations';
        $locations = Location::pickup()->latest()->paginate(getPaginate());
        return view('admin.bus.pickup_locations', compact('pageTitle', 'locations'));
    }

    public function storePickupLocation(Request $request, $id = null)
    {
        if ($id) {
            // Update existing location
            $location = Location::pickup()->findOrFail($id);

            $request->validate([
                'name' => ['required', 'string', 'max:255',
                    Rule::unique('locations')->where('type', 'pickup')->ignore($location->id)],
                'slug' => ['required', 'string', 'max:255', 'regex:/^[a-z0-9-]+$/',
                    Rule::unique('locations')->where('type', 'pickup')->ignore($location->id)]
            ]);

            $location->update([
                'name' => $request->name,
                'slug' => $request->slug,
                'status' => $request->has('status') ? 1 : 0
            ]);

            $notify[] = ['success', 'Pickup location updated successfully'];
        } else {
            // Create new location
            $request->validate([
                'name' => 'required|string|max:255|unique:locations,name,NULL,id,type,pickup',
                'slug' => 'required|string|max:255|regex:/^[a-z0-9-]+$/|unique:locations,slug,NULL,id,type,pickup'
            ]);

            Location::create([
                'name' => $request->name,
                'slug' => $request->slug,
                'type' => Location::TYPE_PICKUP,
                'status' => 1
            ]);

            $notify[] = ['success', 'Pickup location added successfully'];
        }

        return back()->withNotify($notify);
    }



    public function togglePickupLocationStatus($id)
    {
        $location = Location::pickup()->findOrFail($id);
        $location->status = !$location->status;
        $location->save();

        $message = $location->status ? 'Pickup location enabled successfully' : 'Pickup location disabled successfully';
        $notify[] = ['success', $message];
        return back()->withNotify($notify);
    }

    public function deletePickupLocation($id)
    {
        $location = Location::pickup()->findOrFail($id);
        $location->delete();

        $notify[] = ['success', 'Pickup location deleted successfully'];
        return back()->withNotify($notify);
    }

    // Drop Locations
    public function dropLocations()
    {
        $pageTitle = 'Drop Locations';
        $locations = Location::drop()->latest()->paginate(getPaginate());
        return view('admin.bus.drop_locations', compact('pageTitle', 'locations'));
    }

    public function storeDropLocation(Request $request, $id = null)
    {
        if ($id) {
            // Update existing location
            $location = Location::drop()->findOrFail($id);

            $request->validate([
                'name' => ['required', 'string', 'max:255',
                    Rule::unique('locations')->where('type', 'drop')->ignore($location->id)],
                'slug' => ['required', 'string', 'max:255', 'regex:/^[a-z0-9-]+$/',
                    Rule::unique('locations')->where('type', 'drop')->ignore($location->id)]
            ]);

            $location->update([
                'name' => $request->name,
                'slug' => $request->slug,
                'status' => $request->has('status') ? 1 : 0
            ]);

            $notify[] = ['success', 'Drop location updated successfully'];
        } else {
            // Create new location
            $request->validate([
                'name' => 'required|string|max:255|unique:locations,name,NULL,id,type,drop',
                'slug' => 'required|string|max:255|regex:/^[a-z0-9-]+$/|unique:locations,slug,NULL,id,type,drop'
            ]);

            Location::create([
                'name' => $request->name,
                'slug' => $request->slug,
                'type' => Location::TYPE_DROP,
                'status' => 1
            ]);

            $notify[] = ['success', 'Drop location added successfully'];
        }

        return back()->withNotify($notify);
    }



    public function toggleDropLocationStatus($id)
    {
        $location = Location::drop()->findOrFail($id);
        $location->status = !$location->status;
        $location->save();

        $message = $location->status ? 'Drop location enabled successfully' : 'Drop location disabled successfully';
        $notify[] = ['success', $message];
        return back()->withNotify($notify);
    }

    public function deleteDropLocation($id)
    {
        $location = Location::drop()->findOrFail($id);
        $location->delete();

        $notify[] = ['success', 'Drop location deleted successfully'];
        return back()->withNotify($notify);
    }
}
