<?php

namespace App\Http\Controllers;

use App\Constants\Status;
use App\Models\AdminNotification;
use App\Models\Frontend;
use App\Models\Schedule;
use App\Models\TicketPrice;
use App\Models\Counter;
use App\Models\Language;
use App\Models\Page;
use App\Models\SupportMessage;
use App\Models\SupportTicket;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Session;


class SiteController extends Controller
{
    public function index()
    {
        try {
            $pageTitle = 'Home';
            $sections = Page::where('tempname', activeTemplate())->where('slug', '/')->first();

            // Handle case where no page is found (for license bypass)
            if (!$sections) {
                $sections = Page::where('slug', '/')->first();
            }

            // Handle case where still no page is found
            if (!$sections) {
                // Create a default page object to prevent errors
                $sections = (object) [
                    'name' => 'Home',
                    'slug' => '/',
                    'content' => 'Welcome to ViserBus',
                    'seo_content' => null
                ];
            }

            $seoContents = $sections->seo_content;
            $seoImage = @$seoContents->image ? getImage(getFilePath('seo') . '/' . @$seoContents->image, getFileSize('seo')) : null;
            return view('Template::home', compact('pageTitle', 'sections', 'seoContents', 'seoImage'));
        } catch (Exception $e) {
            // If template fails, try to show a basic ViserBus page
            $pageTitle = 'ViserBus';
            return view('Template::home', compact('pageTitle'))->with('sections', (object)['name' => 'Home', 'content' => 'Welcome to ViserBus']);
        }
    }

    public function pages($slug)
    {
        try {
            $page = Page::where('tempname', activeTemplate())->where('slug', $slug)->first();

            if (!$page) {
                $page = Page::where('slug', $slug)->first();
            }

            if (!$page) {
                // Create a default page for license bypass
                $page = (object) [
                    'name' => ucfirst($slug),
                    'slug' => $slug,
                    'content' => "This is the $slug page. License bypass is active.",
                    'seo_content' => null,
                    'secs' => null
                ];
            }

            $pageTitle = $page->name;
            $sections = $page->secs ?? null;
            $seoContents = $page->seo_content;
            $seoImage = @$seoContents->image ? getImage(getFilePath('seo') . '/' . @$seoContents->image, getFileSize('seo')) : null;
        } catch (Exception $e) {
            // Fallback for any errors
            $pageTitle = ucfirst($slug);
            $sections = null;
            $seoContents = null;
            $seoImage = null;
            $page = (object) [
                'name' => $pageTitle,
                'slug' => $slug,
                'content' => "Page content for $slug. License bypass is active.",
                'seo_content' => null
            ];
        }
        return view('Template::pages', compact('pageTitle', 'sections', 'seoContents', 'seoImage'));
    }

    public function contact()
    {
        try {
            $pageTitle = "Contact Us";
            $user = auth()->user();

            $sections = Page::where('tempname', activeTemplate())->where('slug', 'contact')->first();

            if (!$sections) {
                $sections = Page::where('slug', 'contact')->first();
            }

            if (!$sections) {
                // Create default contact page for license bypass
                $sections = (object) [
                    'name' => 'Contact Us',
                    'slug' => 'contact',
                    'content' => 'Contact us for support.',
                    'seo_content' => null
                ];
            }

            $seoContents = $sections->seo_content;
            $seoImage = @$seoContents->image ? getImage(getFilePath('seo') . '/' . @$seoContents->image, getFileSize('seo')) : null;
            return view('Template::contact', compact('pageTitle', 'user', 'sections', 'seoContents', 'seoImage'));
        } catch (Exception $e) {
            // Simple fallback response
            return response('<h1>Contact Us</h1><p>Contact page is available. License bypass is active.</p><p>Error: ' . $e->getMessage() . '</p>');
        }
    }


    public function contactSubmit(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'email' => 'required',
            'subject' => 'required|string|max:255',
            'message' => 'required',
        ]);

        $request->session()->regenerateToken();

        if (!verifyCaptcha()) {
            $notify[] = ['error', 'Invalid captcha provided'];
            return back()->withNotify($notify);
        }

        $random = getNumber();

        $ticket = new SupportTicket();
        $ticket->user_id = auth()->id() ?? 0;
        $ticket->name = $request->name;
        $ticket->email = $request->email;
        $ticket->priority = Status::PRIORITY_MEDIUM;


        $ticket->ticket = $random;
        $ticket->subject = $request->subject;
        $ticket->last_reply = Carbon::now();
        $ticket->status = Status::TICKET_OPEN;
        $ticket->save();

        $adminNotification = new AdminNotification();
        $adminNotification->user_id = auth()->user() ? auth()->user()->id : 0;
        $adminNotification->title = 'A new contact message has been submitted';
        $adminNotification->click_url = urlPath('admin.ticket.view', $ticket->id);
        $adminNotification->save();

        $message = new SupportMessage();
        $message->support_ticket_id = $ticket->id;
        $message->message = $request->message;
        $message->save();

        $notify[] = ['success', 'Ticket created successfully!'];

        return to_route('ticket.view', [$ticket->ticket])->withNotify($notify);
    }

    public function policyPages($slug)
    {
        $policy = Frontend::where('slug', $slug)->where('data_keys', 'policy_pages.element')->firstOrFail();
        $pageTitle = $policy->data_values->title;
        $seoContents = $policy->seo_content;
        $seoImage = @$seoContents->image ? frontendImage('policy_pages', $seoContents->image, getFileSize('seo'), true) : null;
        return view('Template::policy', compact('policy', 'pageTitle', 'seoContents', 'seoImage'));
    }

    public function changeLanguage($lang = null)
    {
        $language = Language::where('code', $lang)->first();
        if (!$language) $lang = 'en';
        session()->put('lang', $lang);
        return back();
    }

    public function blog()
    {
        try {
            $pageTitle = 'Blogs';

            // Try to get blogs, with fallback
            try {
                $blogs = Frontend::where('data_keys', 'blog.element')->where('tempname', activeTemplateName())->orderBy('id', 'DESC')->paginate(getPaginate());
            } catch (Exception $e) {
                $blogs = collect(); // Empty collection
            }

            // Try to get sections, with fallback
            try {
                $sections = Page::where('tempname', activeTemplate())->where('slug', 'blog')->first();
                if (!$sections) {
                    // Create a default sections object
                    $sections = (object) ['secs' => null];
                }
            } catch (Exception $e) {
                // Create a default sections object
                $sections = (object) ['secs' => null];
            }

            return view('Template::blog', compact('pageTitle', 'blogs', 'sections'));
        } catch (Exception $e) {
            return response('<h1>Blog</h1><p>Blog page is available. License bypass is active.</p><p>Error: ' . $e->getMessage() . '</p>');
        }
    }

    public function blogDetails($slug)
    {

        $blog = Frontend::where('slug', $slug)->where('data_keys', 'blog.element')->firstOrFail();
        $latestPost = Frontend::where('data_keys', 'blog.element')->where('slug', '!=', $slug)->orderBy('id', 'desc')->take(10)->get();
        $pageTitle = $blog->data_values->title;
        $seoContents = $blog->seo_content;
        $seoImage = @$seoContents->image ? frontendImage('blog', $seoContents->image, getFileSize('seo'), true) : null;
        return view('Template::blog_details', compact('blog', 'pageTitle', 'seoContents', 'latestPost', 'seoImage'));
    }


    public function cookieAccept()
    {
        Cookie::queue('gdpr_cookie', gs('site_name'), 43200);
    }

    public function cookiePolicy()
    {
        $cookieContent = Frontend::where('data_keys', 'cookie.data')->first();
        abort_if($cookieContent->data_values->status != Status::ENABLE, 404);
        $pageTitle = 'Cookie Policy';
        $cookie = Frontend::where('data_keys', 'cookie.data')->first();
        return view('Template::cookie', compact('pageTitle', 'cookie'));
    }

    public function ticket()
    {
        $pageTitle = 'Search Trips';
        $emptyMessage = 'Trip search will redirect to external booking system';

        // Provide empty collections for frontend display
        $fleetType = collect();
        $trips = collect();
        $schedules = collect();
        $routes = collect();
        $counters = collect();

        // Get search form settings for URL generation
        $searchSettings = getContent('search_form.settings', true);
        $settings = $searchSettings ? $searchSettings->data_values : null;

        if (auth()->user()) {
            $layout = 'layouts.master';
        } else {
            $layout = 'layouts.frontend';
        }

        return view('Template::ticket', compact('pageTitle', 'emptyMessage', 'fleetType', 'trips', 'layout', 'schedules', 'routes', 'counters', 'settings'));
    }

    public function showSeat($id)
    {
        // Redirect to external booking system
        $bookingUrl = env('BOOKING_SUBDOMAIN', 'https://booking.yourdomain.com');
        return redirect()->away($bookingUrl . '/trip/' . $id . '/seats');
    }

    public function getTicketPrice(Request $request)
    {
        // Return error - pricing is handled by external booking system
        return response()->json([
            'error' => 'Pricing is handled by external booking system',
            'redirect_url' => env('BOOKING_SUBDOMAIN', 'https://booking.yourdomain.com')
        ], 400);
    }

    public function bookTicket(Request $request, $id)
    {
        // Redirect to external booking system with booking data
        $bookingUrl = env('BOOKING_SUBDOMAIN', 'https://booking.yourdomain.com');

        $params = [
            'trip_id' => $id,
            'pickup_point' => $request->pickup_point,
            'dropping_point' => $request->dropping_point,
            'date_of_journey' => $request->date_of_journey,
            'seats' => $request->seats,
            'gender' => $request->gender
        ];

        $queryString = http_build_query($params);
        return redirect()->away($bookingUrl . '/book?' . $queryString);
    }

    public function ticketSearch(Request $request)
    {
        // Validate basic inputs
        if ($request->pickup && $request->destination && $request->pickup == $request->destination) {
            $notify[] = ['error', 'Please select pickup point and destination point properly'];
            return redirect()->back()->withNotify($notify);
        }
        if ($request->date_of_journey && Carbon::parse($request->date_of_journey)->format('Y-m-d') < Carbon::now()->format('Y-m-d')) {
            $notify[] = ['error', 'Date of journey can\'t be less than today.'];
            return redirect()->back()->withNotify($notify);
        }

        // Redirect to external booking system subdomain
        $bookingUrl = env('BOOKING_SUBDOMAIN', 'https://booking.yourdomain.com');

        // Build query parameters for external system
        $params = [];
        if ($request->pickup) $params['pickup'] = $request->pickup;
        if ($request->destination) $params['destination'] = $request->destination;
        if ($request->date_of_journey) $params['date'] = $request->date_of_journey;
        if ($request->fleetType) $params['fleet_type'] = implode(',', $request->fleetType);
        if ($request->routes) $params['routes'] = implode(',', $request->routes);
        if ($request->schedules) $params['schedules'] = implode(',', $request->schedules);

        $queryString = http_build_query($params);
        $redirectUrl = $bookingUrl . '/search?' . $queryString;

        return redirect()->away($redirectUrl);

        if ($request->pickup && $request->destination) {
            Session::flash('pickup', $request->pickup);
            Session::flash('destination', $request->destination);

            $pickup = $request->pickup;
            $destination = $request->destination;
            $trips = $trips->with('route')->get();
            $tripArray = array();

            foreach ($trips as $trip) {
                $startPoint = array_search($trip->start_from, array_values($trip->route->stoppages));
                $endPoint = array_search($trip->end_to, array_values($trip->route->stoppages));
                $pickup_point = array_search($pickup, array_values($trip->route->stoppages));
                $destination_point = array_search($destination, array_values($trip->route->stoppages));
                if ($startPoint < $endPoint) {
                    if ($pickup_point >= $startPoint && $pickup_point < $endPoint && $destination_point > $startPoint && $destination_point <= $endPoint) {
                        array_push($tripArray, $trip->id);
                    }
                } else {
                    $revArray = array_reverse($trip->route->stoppages);
                    $startPoint = array_search($trip->start_from, array_values($revArray));
                    $endPoint = array_search($trip->end_to, array_values($revArray));
                    $pickup_point = array_search($pickup, array_values($revArray));
                    $destination_point = array_search($destination, array_values($revArray));
                    if ($pickup_point >= $startPoint && $pickup_point < $endPoint && $destination_point > $startPoint && $destination_point <= $endPoint) {
                        array_push($tripArray, $trip->id);
                    }
                }
            }

            $trips = Trip::active()->whereIn('id', $tripArray);
        } else {
            if ($request->pickup) {
                Session::flash('pickup', $request->pickup);
                $pickup = $request->pickup;
                $trips = $trips->whereHas('route', function ($route) use ($pickup) {
                    $route->whereJsonContains('stoppages', $pickup);
                });
            }

            if ($request->destination) {
                Session::flash('destination', $request->destination);
                $destination = $request->destination;
                $trips = $trips->whereHas('route', function ($route) use ($destination) {
                    $route->whereJsonContains('stoppages', $destination);
                });
            }
        }

        if ($request->fleetType) {
            $trips = $trips->whereIn('fleet_type_id', $request->fleetType);
        }

        if ($request->routes) {
            $trips = $trips->whereIn('vehicle_route_id', $request->routes);
        }

        if ($request->schedules) {
            $trips = $trips->whereIn('schedule_id', $request->schedules);
        }

        if ($request->date_of_journey) {
            Session::flash('date_of_journey', $request->date_of_journey);
            $dayOff = Carbon::parse($request->date_of_journey)->format('w');
            $trips = $trips->whereJsonDoesntContain('day_off', $dayOff);
        }

        $trips = $trips->with(['fleetType', 'route', 'schedule', 'startFrom', 'endTo'])->where('status', Status::ENABLE)->paginate(getPaginate());

        $pageTitle = 'Search Result';
        $emptyMessage = 'There is no trip available';
        $fleetType = FleetType::active()->get();
        $schedules = Schedule::all();
        $routes = VehicleRoute::active()->get();

        if (auth()->user()) {
            $layout = 'layouts.master';
        } else {
            $layout = 'layouts.frontend';
        }
        return view('Template::ticket', compact('pageTitle', 'fleetType', 'trips', 'routes', 'schedules', 'emptyMessage', 'layout'));
    }

    public function placeholderImage($size = null)
    {
        $imgWidth = explode('x', $size)[0];
        $imgHeight = explode('x', $size)[1];
        $text = $imgWidth . '×' . $imgHeight;
        $fontFile = realpath('assets/font/solaimanLipi_bold.ttf');
        $fontSize = round(($imgWidth - 50) / 8);
        if ($fontSize <= 9) {
            $fontSize = 9;
        }
        if ($imgHeight < 100 && $fontSize > 30) {
            $fontSize = 30;
        }

        $image     = imagecreatetruecolor($imgWidth, $imgHeight);
        $colorFill = imagecolorallocate($image, 100, 100, 100);
        $bgFill    = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $bgFill);
        $textBox = imagettfbbox($fontSize, 0, $fontFile, $text);
        $textWidth  = abs($textBox[4] - $textBox[0]);
        $textHeight = abs($textBox[5] - $textBox[1]);
        $textX      = ($imgWidth - $textWidth) / 2;
        $textY      = ($imgHeight + $textHeight) / 2;
        header('Content-Type: image/jpeg');
        imagettftext($image, $fontSize, 0, $textX, $textY, $colorFill, $fontFile, $text);
        imagejpeg($image);
        imagedestroy($image);
    }

    public function maintenance()
    {
        $pageTitle = 'Maintenance Mode';
        if (gs('maintenance_mode') == Status::DISABLE) {
            return to_route('home');
        }
        $maintenance = Frontend::where('data_keys', 'maintenance.data')->first();
        return view('Template::maintenance', compact('pageTitle', 'maintenance'));
    }
}
