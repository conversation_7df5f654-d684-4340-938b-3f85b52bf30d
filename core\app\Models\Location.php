<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Location extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'type',
        'status',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    // Constants for location types
    const TYPE_PICKUP = 'pickup';
    const TYPE_DROP = 'drop';

    // Scopes
    public function scopePickup($query)
    {
        return $query->where('type', self::TYPE_PICKUP);
    }

    public function scopeDrop($query)
    {
        return $query->where('type', self::TYPE_DROP);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 0);
    }
}
