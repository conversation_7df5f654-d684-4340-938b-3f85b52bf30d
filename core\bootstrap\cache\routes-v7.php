<?php

app('router')->setCompiledRoutes(
    array (
  'compiled' => 
  array (
    0 => false,
    1 => 
    array (
      '/_debugbar/open' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.openhandler',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_debugbar/assets/stylesheets' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.assets.css',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_debugbar/assets/javascript' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.assets.js',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_debugbar/queries/explain' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.queries.explain',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/sanctum/csrf-cookie' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'sanctum.csrf-cookie',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_ignition/health-check' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ignition.healthCheck',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_ignition/execute-solution' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ignition.executeSolution',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_ignition/update-config' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ignition.updateConfig',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/general-setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/get-countries' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::RVc7KOuJwAv5FUvt',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/policies' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::BnjlZg7xMLcXYSHI',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/faq' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::GZUryJB1j85ZJ8Lm',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/login' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::3l9fvNrXNxzDPR7F',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/check-token' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::A9CENLQsWJtpFsqQ',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/social-login' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::8Qsx2h2062Jlftut',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/register' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::ta9O3IFh56alfDzL',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/password/email' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::IM0S4psfL4CwsR6z',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/password/verify-code' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::bX9YY8Ra6CfuibRa',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/password/reset' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::vV3cR9UnMWw6tr6u',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/user-data-submit' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::q2uWkqf0WR505F9r',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/authorization' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::YQ9CHBWm01xFr7aX',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/verify-email' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::VGqqQdXiU0o11FOG',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/verify-mobile' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::orrpG7JPB0GKrCyF',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/verify-g2fa' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::bt5D9xojncQs8qIi',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/dashboard' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::zd2Emtw3WVgvG4N3',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/profile-setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::eV7HABOdGscSAhQa',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/change-password' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::ELsx8apHBSNVv5QB',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/user-info' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::FZWoTk2cNx8LSi34',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/deposit/history' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::IBTjj6Qoa2cWW6Fx',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
            'POST' => 2,
            'PUT' => 3,
            'PATCH' => 4,
            'DELETE' => 5,
            'OPTIONS' => 6,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/transactions' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::N2R9ifH7wgFUl6RT',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/add-device-token' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::fKU5H1RdKXBFsCUJ',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/push-notifications' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::6237r1HE81V01lFx',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/twofactor' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::T5U6feHKmLyjFepn',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/twofactor/enable' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::DujoNotPDulC2YYV',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/twofactor/disable' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::ZQ0YY60faTet5XMV',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/delete-account' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::d4WEhvravBp4rAoC',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/deposit/methods' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::vwlUZYCnw7YGjrj2',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/deposit/insert' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::PtAEvs0FdWDEd82S',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/ticket' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::nkaLF8goOBhpZTvL',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/ticket/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::DUaXwqGcpwiz3W8p',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/logout' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::ZTWmghpzbCzpXiVV',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.login',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/logout' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.logout',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/password/reset' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password.reset',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password.',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/password/code-verify' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password.code.verify',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/password/verify-code' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password.verify.code',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/dashboard' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.dashboard',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/payment-chart' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.payment.chart',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/profile' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.profile',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.profile.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/password' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notifications' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.notifications',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notifications/read-all' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.notifications.read.all',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notifications/delete-all' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.notifications.delete.all',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/request-report' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.request.report',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::9Va0tFxGyW686Qmo',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/bus/pickup/locations' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.pickup.locations',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/bus/drop/locations' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.drop.locations',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/counter' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.counter.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/fleet/layouts' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.layouts.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/fleet/type' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.type.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/fleet/vehicles' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.vehicles.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/manage/trip' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.list',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/manage/route' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.route.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/manage/route/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.route.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/manage/route/store' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.route.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/manage/schedule' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.schedule.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/manage/assigned-vehicle' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.vehicle.assign.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.all',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/active' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.active',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/banned' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.banned',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/email-verified' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.email.verified',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/email-unverified' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.email.unverified',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/mobile-unverified' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.mobile.unverified',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/mobile-verified' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.mobile.verified',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/send-notification' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.notification.all',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.notification.all.send',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/users/list' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.list',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/gateway/automatic' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.automatic.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/gateway/manual' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.manual.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/gateway/manual/new' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.manual.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.manual.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/deposit/reject' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.reject',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/report/login/history' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.report.login.history',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/report/notification/history' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.report.notification.history',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/ticket' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/ticket/pending' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.pending',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/ticket/closed' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.closed',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/ticket/answered' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.answered',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/language' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.manage',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.manage.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/language/import' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.import.lang',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/language/get-keys' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.get.key',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/system-setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.system',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/general-setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.general',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::bWVNrtG5bqVOikW9',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/setting/social/credentials' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.socialite.credentials',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/setting/system-configuration' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.system.configuration',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::fyuH8y6IvBy3hNWt',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/setting/logo-icon' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.logo.icon',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::AU0IAmUU6Dm0AY6x',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/custom-css' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.custom.css',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::f0k2rwKWMNaYWnAU',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sitemap' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.sitemap',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::pJNtW36YG2lv69jm',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/robot' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.robot',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::XGi0J4PJvRCaIRio',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/search-form-settings' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.search.form',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::puOgJELfj2fn6r8j',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/cookie' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.cookie',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::Vp7UmC9c958qNHcA',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/maintenance-mode' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.maintenance.mode',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.generated::iyx9yuppQkCRUMO6',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/global/email' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.global.email',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/global/email/update' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.global.email.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/global/sms' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.global.sms',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/global/sms/update' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.global.sms.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/global/push' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.global.push',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/global/push/update' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.global.push.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/templates' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.templates',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/email/setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.email',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/email/test' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.email.test',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/sms/setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.sms',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.generated::MU0GyEF4IUeRVHwe',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/sms/test' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.sms.test',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/notification/push/setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.push',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.generated::y6NULpTyDJeEVgsp',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/notification/push/setting/upload' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.push.upload',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/notification/notification/push/setting/download' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.push.download',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/extensions' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.extensions.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/system/info' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.system.info',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/system/server-info' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.system.server.info',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/system/optimize' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.system.optimize',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/system/optimize-clear' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.system.optimize.clear',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/system/system-update' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.system.update',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.system.update.process',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/system/system-update/log' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.system.update.log',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/seo' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.seo',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/frontend/index' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/frontend/templates' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.templates',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.templates.active',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/frontend/manage-pages' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.pages',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.pages.save',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/frontend/manage-pages/update' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.pages.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/paypal' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Paypal',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/paypal-sdk' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.PaypalSdk',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/perfect-money' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.PerfectMoney',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/stripe' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Stripe',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/stripe-js' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.StripeJs',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/stripe-v3' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.StripeV3',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/skrill' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Skrill',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/paytm' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Paytm',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/payeer' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Payeer',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/paystack' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Paystack',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/razorpay' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Razorpay',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/instamojo' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Instamojo',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/blockchain' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Blockchain',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/coinpayments' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Coinpayments',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/coinpayments-fiat' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.CoinpaymentsFiat',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/coingate' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Coingate',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/coinbase-commerce' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.CoinbaseCommerce',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/mollie' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Mollie',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/cashmaal' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Cashmaal',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/mercado-pago' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.MercadoPago',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/authorize' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Authorize',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/nmi' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.NMI',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/btc-pay' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.BTCPay',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
            'POST' => 2,
            'PUT' => 3,
            'PATCH' => 4,
            'DELETE' => 5,
            'OPTIONS' => 6,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/now-payments-hosted' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.NowPaymentsHosted',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/now-payments-checkout' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.NowPaymentsCheckout',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/2checkout' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.TwoCheckout',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/checkout' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Checkout',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
            'POST' => 2,
            'PUT' => 3,
            'PATCH' => 4,
            'DELETE' => 5,
            'OPTIONS' => 6,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/sslcommerz' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.SslCommerz',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/aamarpay' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Aamarpay',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ipn/binance' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Binance',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/login' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.login',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'user.',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/logout' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.logout',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/register' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.register',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'user.generated::j21KbmSmEyrBpDhy',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/check-user' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.checkUser',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/password/reset' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.password.request',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'user.password.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/password/email' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.password.email',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/password/code-verify' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.password.code.verify',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/password/verify-code' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.password.verify.code',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/user-data' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.data',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/user-data-submit' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.data.submit',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/authorization' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.authorization',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/verify-email' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.verify.email',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/verify-mobile' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.verify.mobile',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/verify-g2fa' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.2fa.verify',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/dashboard' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.home',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/twofactor' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.twofactor',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/twofactor/enable' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.twofactor.enable',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/twofactor/disable' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.twofactor.disable',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/booked-ticket/history' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.ticket.history',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/payment/history' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.deposit.history',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
            'POST' => 2,
            'PUT' => 3,
            'PATCH' => 4,
            'DELETE' => 5,
            'OPTIONS' => 6,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/transactions' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.transactions',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/add-device-token' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.add.device.token',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/profile-setting' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.profile.setting',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'user.generated::hoJJoJrxyPOqeo8C',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/change-password' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.change.password',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'user.generated::XLOrQlMAs1iJSSGs',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/payment' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.deposit.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
            'POST' => 2,
            'PUT' => 3,
            'PATCH' => 4,
            'DELETE' => 5,
            'OPTIONS' => 6,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/payment/insert' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.deposit.insert',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/payment/confirm' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.deposit.confirm',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/user/payment/manual' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.deposit.manual.confirm',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'user.deposit.manual.update',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/clear' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::Vp1phRGbWGL4EOoh',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ticket' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ticket/new' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.open',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ticket/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/contact' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'contact',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'generated::DlehXO0aS2BRuI7y',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/cookie-policy' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'cookie.policy',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/cookie/accept' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'cookie.accept',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/blog' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'blog',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/cookie/details' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'cookie.details',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/maintenance-mode' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'maintenance',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/tickets' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ticket/get-price' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.get-price',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/ticket/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/bypass-success' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'bypass.success',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'home',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
    ),
    2 => 
    array (
      0 => '{^(?|/_debugbar/c(?|lockwork/([^/]++)(*:39)|ache/([^/]++)(?:/([^/]++))?(*:73))|/a(?|p(?|i/(?|language/([^/]++)(*:112)|resend\\-verify/([^/]++)(*:143)|push\\-notifications/read/([^/]++)(*:184)|ticket/(?|view/([^/]++)(*:215)|reply/([^/]++)(*:237)|close/([^/]++)(*:259)|download/([^/]++)(*:284)))|p/deposit/confirm/([^/]++)(*:320))|dmin/(?|password/reset/(?|([^/]++)(*:363)|change(*:377))|notification(?|/(?|read/([^/]++)(*:418)|template/(?|edit/([^/]++)/([^/]++)(*:460)|update/([^/]++)/([^/]++)(*:492)))|s/delete\\-single/([^/]++)(*:527))|d(?|ownload\\-attachments/([^/]++)(*:569)|eposit/(?|a(?|ll(?:/([^/]++))?(*:607)|pprove(?|d(?:/([^/]++))?(*:639)|/([^/]++)(*:656)))|pending(?:/([^/]++))?(*:687)|rejected(?:/([^/]++))?(*:717)|successful(?:/([^/]++))?(*:749)|initiated(?:/([^/]++))?(*:780)|details/([^/]++)(*:804)))|bus/(?|pickup/locations/(?|st(?|ore(?:/([^/]++))?(*:863)|atus/([^/]++)(*:884))|delete/([^/]++)(*:908))|drop/locations/(?|st(?|ore(?:/([^/]++))?(*:957)|atus/([^/]++)(*:978))|delete/([^/]++)(*:1002)))|counter(?|(?:/([^/]++))?(*:1037)|/status/([^/]++)(*:1062))|f(?|leet/(?|layouts/(?|store(?:/([^/]++))?(*:1114)|remove/([^/]++)(*:1138))|type/st(?|ore(?:/([^/]++))?(*:1175)|atus/([^/]++)(*:1197))|vehicles/st(?|ore(?:/([^/]++))?(*:1238)|atus/([^/]++)(*:1260)))|rontend/(?|frontend\\-(?|s(?|ections(?:/([^/]++))?(*:1320)|lug\\-check/([^/]++)(?:/([^/]++))?(*:1362))|content/([^/]++)(*:1388)|element(?|/([^/]++)(?:/([^/]++))?(*:1430)|\\-seo/([^/]++)/([^/]++)(?|(*:1465))))|remove/([^/]++)(*:1492)|manage\\-(?|pages/(?|check\\-slug(?:/([^/]++))?(*:1546)|delete/([^/]++)(*:1570))|se(?|ction/([^/]++)(?|(*:1602))|o/([^/]++)(?|(*:1625))))))|manage/(?|trip/st(?|ore(?:/([^/]++))?(*:1676)|atus/([^/]++)(*:1698))|route/(?|edit/([^/]++)(*:1730)|update/([^/]++)(*:1754)|status/([^/]++)(*:1778))|schedule/st(?|ore(?:/([^/]++))?(*:1819)|atus/([^/]++)(*:1841))|assigned\\-vehicle/st(?|ore(?:/([^/]++))?(*:1891)|atus/([^/]++)(*:1913)))|users/(?|detail/([^/]++)(*:1948)|update/([^/]++)(*:1972)|add\\-sub\\-balance/([^/]++)(*:2007)|s(?|end\\-notification/([^/]++)(?|(*:2049))|tatus/([^/]++)(*:2073))|login/([^/]++)(*:2097)|count\\-by\\-segment/([^/]++)(*:2133)|notification\\-log/([^/]++)(*:2168))|gateway/(?|automatic/(?|edit/([^/]++)(*:2215)|update/([^/]++)(*:2239)|remove/([^/]++)(*:2263)|status/([^/]++)(*:2287))|manual/(?|edit/([^/]++)(*:2320)|update/([^/]++)(*:2344)|status/([^/]++)(*:2368)))|report/(?|transaction(?:/([^/]++))?(*:2414)|login/ipHistory/([^/]++)(*:2447)|email/detail/([^/]++)(*:2477))|ticket/(?|view/([^/]++)(*:2510)|reply/([^/]++)(*:2533)|close/([^/]++)(*:2556)|d(?|ownload/([^/]++)(*:2585)|elete/([^/]++)(*:2608)))|language/(?|delete/(?|([^/]++)(*:2649)|key/([^/]++)(*:2670))|update/(?|([^/]++)(*:2698)|key/([^/]++)(*:2719))|edit/([^/]++)(*:2742)|store/key/([^/]++)(*:2769))|setting/social/credentials/(?|update/([^/]++)(*:2824)|status/([^/]++)(*:2848))|extensions/(?|update/([^/]++)(*:2887)|status/([^/]++)(*:2911))))|/ipn/flutterwave/([^/]++)/([^/]++)(*:2957)|/user/(?|password/reset/([^/]++)(*:2998)|social\\-login/(?|([^/]++)(*:3032)|callback/([^/]++)(*:3058))|resend\\-verify/([^/]++)(*:3091)|download\\-attachments/([^/]++)(*:3130)|booked\\-ticket/print/([^/]++)(*:3168))|/ticket/(?|view/([^/]++)(*:3202)|reply/([^/]++)(*:3225)|close/([^/]++)(*:3248)|download/([^/]++)(*:3274)|([^/]++)/([^/]++)(*:3300)|book/([^/]++)(*:3322))|/change(?:/([^/]++))?(*:3353)|/blog/([^/]++)(*:3376)|/p(?|olicy/([^/]++)(*:3404)|laceholder\\-image/([^/]++)(*:3439))|/([^/]++)(*:3458))/?$}sDu',
    ),
    3 => 
    array (
      39 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.clockwork',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      73 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.cache.delete',
            'tags' => NULL,
          ),
          1 => 
          array (
            0 => 'key',
            1 => 'tags',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      112 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::JjJ2GH3LuLi1llpn',
          ),
          1 => 
          array (
            0 => 'key',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      143 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::SBjUKD1qm7gcKAC6',
          ),
          1 => 
          array (
            0 => 'type',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      184 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::GXbqls8dW1Xc96H6',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      215 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::HqLtH1GwuXEHBN1Q',
          ),
          1 => 
          array (
            0 => 'ticket',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      237 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::LBi1bgNgpT5fZdzN',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      259 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::cuUFe1HZhuWDV9v6',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      284 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'api.generated::7QPZHGNgeh5qeTBC',
          ),
          1 => 
          array (
            0 => 'attachment_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      320 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'deposit.app.confirm',
          ),
          1 => 
          array (
            0 => 'hash',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      363 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password.reset.form',
          ),
          1 => 
          array (
            0 => 'token',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      377 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.password.change',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      418 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.notification.read',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      460 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.template.edit',
          ),
          1 => 
          array (
            0 => 'type',
            1 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      492 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.notification.template.update',
          ),
          1 => 
          array (
            0 => 'type',
            1 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      527 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.notifications.delete.single',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      569 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.download.attachment',
          ),
          1 => 
          array (
            0 => 'file_hash',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      607 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.list',
            'user_id' => NULL,
          ),
          1 => 
          array (
            0 => 'user_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      639 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.approved',
            'user_id' => NULL,
          ),
          1 => 
          array (
            0 => 'user_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      656 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.approve',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      687 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.pending',
            'user_id' => NULL,
          ),
          1 => 
          array (
            0 => 'user_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      717 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.rejected',
            'user_id' => NULL,
          ),
          1 => 
          array (
            0 => 'user_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      749 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.successful',
            'user_id' => NULL,
          ),
          1 => 
          array (
            0 => 'user_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      780 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.initiated',
            'user_id' => NULL,
          ),
          1 => 
          array (
            0 => 'user_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      804 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.deposit.details',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      863 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.pickup.locations.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      884 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.pickup.locations.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      908 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.pickup.locations.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      957 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.drop.locations.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      978 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.drop.locations.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1002 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.bus.drop.locations.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1037 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.counter.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1062 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.counter.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1114 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.layouts.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1138 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.layouts.remove',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1175 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.type.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1197 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.type.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1238 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.vehicles.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1260 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.fleet.vehicles.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1320 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.sections',
            'key' => NULL,
          ),
          1 => 
          array (
            0 => 'key',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1362 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.sections.element.slug.check',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'key',
            1 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1388 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.sections.content',
          ),
          1 => 
          array (
            0 => 'key',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1430 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.sections.element',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'key',
            1 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1465 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.sections.element.seo',
          ),
          1 => 
          array (
            0 => 'key',
            1 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.',
          ),
          1 => 
          array (
            0 => 'key',
            1 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1492 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.remove',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1546 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.pages.check.slug',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1570 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.pages.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1602 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.section',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.section.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1625 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.manage.pages.seo',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.frontend.generated::ZDsa49HsBNMrIPgg',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1676 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1698 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1730 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.route.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1754 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.route.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1778 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.route.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1819 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.schedule.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1841 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.schedule.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1891 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.vehicle.assign.store',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1913 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.trip.vehicle.assign.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1948 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.detail',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1972 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2007 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.add.sub.balance',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2049 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.notification.single',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2073 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2097 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.login',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2133 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.segment.count',
          ),
          1 => 
          array (
            0 => 'methodName',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2168 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.users.notification.log',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2215 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.automatic.edit',
          ),
          1 => 
          array (
            0 => 'alias',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2239 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.automatic.update',
          ),
          1 => 
          array (
            0 => 'code',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2263 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.automatic.remove',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2287 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.automatic.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2320 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.manual.edit',
          ),
          1 => 
          array (
            0 => 'alias',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2344 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.manual.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2368 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.gateway.manual.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2414 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.report.transaction',
            'user_id' => NULL,
          ),
          1 => 
          array (
            0 => 'user_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2447 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.report.login.ipHistory',
          ),
          1 => 
          array (
            0 => 'ip',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2477 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.report.email.details',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2510 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2533 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.reply',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2556 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.close',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2585 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.download',
          ),
          1 => 
          array (
            0 => 'attachment_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2608 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.ticket.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2649 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.manage.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2670 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.delete.key',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2698 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.manage.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2719 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.update.key',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2742 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.key',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2769 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.language.store.key',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2824 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.socialite.credentials.update',
          ),
          1 => 
          array (
            0 => 'key',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2848 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.setting.socialite.credentials.status.update',
          ),
          1 => 
          array (
            0 => 'key',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2887 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.extensions.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2911 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.extensions.status',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2957 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ipn.Flutterwave',
          ),
          1 => 
          array (
            0 => 'trx',
            1 => 'type',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2998 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.password.reset',
          ),
          1 => 
          array (
            0 => 'token',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3032 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.social.login',
          ),
          1 => 
          array (
            0 => 'provider',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3058 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.social.login.callback',
          ),
          1 => 
          array (
            0 => 'provider',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3091 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.send.verify.code',
          ),
          1 => 
          array (
            0 => 'type',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3130 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.download.attachment',
          ),
          1 => 
          array (
            0 => 'file_hash',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3168 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'user.ticket.print',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3202 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.view',
          ),
          1 => 
          array (
            0 => 'ticket',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3225 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.reply',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3248 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.close',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3274 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.download',
          ),
          1 => 
          array (
            0 => 'attachment_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3300 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.seats',
          ),
          1 => 
          array (
            0 => 'id',
            1 => 'slug',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3322 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ticket.book',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3353 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'lang',
            'lang' => NULL,
          ),
          1 => 
          array (
            0 => 'lang',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3376 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'blog.details',
          ),
          1 => 
          array (
            0 => 'slug',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3404 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'policy.pages',
          ),
          1 => 
          array (
            0 => 'slug',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3439 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'placeholder.image',
          ),
          1 => 
          array (
            0 => 'size',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3458 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'pages',
          ),
          1 => 
          array (
            0 => 'slug',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => NULL,
          1 => NULL,
          2 => NULL,
          3 => NULL,
          4 => false,
          5 => false,
          6 => 0,
        ),
      ),
    ),
    4 => NULL,
  ),
  'attributes' => 
  array (
    'debugbar.openhandler' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/open',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@handle',
        'as' => 'debugbar.openhandler',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@handle',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.clockwork' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/clockwork/{id}',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@clockwork',
        'as' => 'debugbar.clockwork',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@clockwork',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.assets.css' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/assets/stylesheets',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@css',
        'as' => 'debugbar.assets.css',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@css',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.assets.js' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/assets/javascript',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@js',
        'as' => 'debugbar.assets.js',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@js',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.cache.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => '_debugbar/cache/{key}/{tags?}',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\CacheController@delete',
        'as' => 'debugbar.cache.delete',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\CacheController@delete',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.queries.explain' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => '_debugbar/queries/explain',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\QueriesController@explain',
        'as' => 'debugbar.queries.explain',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\QueriesController@explain',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'sanctum.csrf-cookie' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'sanctum/csrf-cookie',
      'action' => 
      array (
        'uses' => 'Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show',
        'controller' => 'Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show',
        'namespace' => NULL,
        'prefix' => 'sanctum',
        'where' => 
        array (
        ),
        'middleware' => 
        array (
          0 => 'web',
        ),
        'as' => 'sanctum.csrf-cookie',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ignition.healthCheck' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_ignition/health-check',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled',
        ),
        'uses' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController@__invoke',
        'controller' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController',
        'as' => 'ignition.healthCheck',
        'namespace' => NULL,
        'prefix' => '_ignition',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ignition.executeSolution' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => '_ignition/execute-solution',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled',
        ),
        'uses' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\ExecuteSolutionController@__invoke',
        'controller' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\ExecuteSolutionController',
        'as' => 'ignition.executeSolution',
        'namespace' => NULL,
        'prefix' => '_ignition',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ignition.updateConfig' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => '_ignition/update-config',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled',
        ),
        'uses' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\UpdateConfigController@__invoke',
        'controller' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\UpdateConfigController',
        'as' => 'ignition.updateConfig',
        'namespace' => NULL,
        'prefix' => '_ignition',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/general-setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AppController@generalSetting',
        'controller' => 'App\\Http\\Controllers\\Api\\AppController@generalSetting',
        'as' => 'api.',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::RVc7KOuJwAv5FUvt' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/get-countries',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AppController@getCountries',
        'controller' => 'App\\Http\\Controllers\\Api\\AppController@getCountries',
        'as' => 'api.generated::RVc7KOuJwAv5FUvt',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::JjJ2GH3LuLi1llpn' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/language/{key}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AppController@getLanguage',
        'controller' => 'App\\Http\\Controllers\\Api\\AppController@getLanguage',
        'as' => 'api.generated::JjJ2GH3LuLi1llpn',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::BnjlZg7xMLcXYSHI' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/policies',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AppController@policies',
        'controller' => 'App\\Http\\Controllers\\Api\\AppController@policies',
        'as' => 'api.generated::BnjlZg7xMLcXYSHI',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::GZUryJB1j85ZJ8Lm' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/faq',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AppController@faq',
        'controller' => 'App\\Http\\Controllers\\Api\\AppController@faq',
        'as' => 'api.generated::GZUryJB1j85ZJ8Lm',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::3l9fvNrXNxzDPR7F' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/login',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@login',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@login',
        'as' => 'api.generated::3l9fvNrXNxzDPR7F',
        'namespace' => 'App\\Http\\Controllers\\Api\\Auth',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::A9CENLQsWJtpFsqQ' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/check-token',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@checkToken',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@checkToken',
        'as' => 'api.generated::A9CENLQsWJtpFsqQ',
        'namespace' => 'App\\Http\\Controllers\\Api\\Auth',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::8Qsx2h2062Jlftut' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/social-login',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@socialLogin',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@socialLogin',
        'as' => 'api.generated::8Qsx2h2062Jlftut',
        'namespace' => 'App\\Http\\Controllers\\Api\\Auth',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::ta9O3IFh56alfDzL' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/register',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\RegisterController@register',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\RegisterController@register',
        'as' => 'api.generated::ta9O3IFh56alfDzL',
        'namespace' => 'App\\Http\\Controllers\\Api\\Auth',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::IM0S4psfL4CwsR6z' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/password/email',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\ForgotPasswordController@sendResetCodeEmail',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\ForgotPasswordController@sendResetCodeEmail',
        'as' => 'api.generated::IM0S4psfL4CwsR6z',
        'namespace' => 'App\\Http\\Controllers\\Api\\Auth',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::bX9YY8Ra6CfuibRa' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/password/verify-code',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\ForgotPasswordController@verifyCode',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\ForgotPasswordController@verifyCode',
        'as' => 'api.generated::bX9YY8Ra6CfuibRa',
        'namespace' => 'App\\Http\\Controllers\\Api\\Auth',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::vV3cR9UnMWw6tr6u' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/password/reset',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\ForgotPasswordController@reset',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\ForgotPasswordController@reset',
        'as' => 'api.generated::vV3cR9UnMWw6tr6u',
        'namespace' => 'App\\Http\\Controllers\\Api\\Auth',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::q2uWkqf0WR505F9r' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/user-data-submit',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@userDataSubmit',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@userDataSubmit',
        'as' => 'api.generated::q2uWkqf0WR505F9r',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::YQ9CHBWm01xFr7aX' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/authorization',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AuthorizationController@authorization',
        'controller' => 'App\\Http\\Controllers\\Api\\AuthorizationController@authorization',
        'as' => 'api.generated::YQ9CHBWm01xFr7aX',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::SBjUKD1qm7gcKAC6' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/resend-verify/{type}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AuthorizationController@sendVerifyCode',
        'controller' => 'App\\Http\\Controllers\\Api\\AuthorizationController@sendVerifyCode',
        'as' => 'api.generated::SBjUKD1qm7gcKAC6',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::VGqqQdXiU0o11FOG' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/verify-email',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AuthorizationController@emailVerification',
        'controller' => 'App\\Http\\Controllers\\Api\\AuthorizationController@emailVerification',
        'as' => 'api.generated::VGqqQdXiU0o11FOG',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::orrpG7JPB0GKrCyF' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/verify-mobile',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AuthorizationController@mobileVerification',
        'controller' => 'App\\Http\\Controllers\\Api\\AuthorizationController@mobileVerification',
        'as' => 'api.generated::orrpG7JPB0GKrCyF',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::bt5D9xojncQs8qIi' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/verify-g2fa',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\AuthorizationController@g2faVerification',
        'controller' => 'App\\Http\\Controllers\\Api\\AuthorizationController@g2faVerification',
        'as' => 'api.generated::bt5D9xojncQs8qIi',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::zd2Emtw3WVgvG4N3' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/dashboard',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'O:55:"Laravel\\SerializableClosure\\UnsignedSerializableClosure":1:{s:12:"serializable";O:46:"Laravel\\SerializableClosure\\Serializers\\Native":5:{s:3:"use";a:0:{}s:8:"function";s:73:"function(){
                    return \\auth()->user();
                }";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"00000000000005100000000000000000";}}',
        'as' => 'api.generated::zd2Emtw3WVgvG4N3',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::eV7HABOdGscSAhQa' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/profile-setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@submitProfile',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@submitProfile',
        'as' => 'api.generated::eV7HABOdGscSAhQa',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::ELsx8apHBSNVv5QB' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/change-password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@submitPassword',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@submitPassword',
        'as' => 'api.generated::ELsx8apHBSNVv5QB',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::FZWoTk2cNx8LSi34' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/user-info',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@userInfo',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@userInfo',
        'as' => 'api.generated::FZWoTk2cNx8LSi34',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::IBTjj6Qoa2cWW6Fx' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
        2 => 'POST',
        3 => 'PUT',
        4 => 'PATCH',
        5 => 'DELETE',
        6 => 'OPTIONS',
      ),
      'uri' => 'api/deposit/history',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@depositHistory',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@depositHistory',
        'as' => 'api.generated::IBTjj6Qoa2cWW6Fx',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::N2R9ifH7wgFUl6RT' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/transactions',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@transactions',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@transactions',
        'as' => 'api.generated::N2R9ifH7wgFUl6RT',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::fKU5H1RdKXBFsCUJ' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/add-device-token',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@addDeviceToken',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@addDeviceToken',
        'as' => 'api.generated::fKU5H1RdKXBFsCUJ',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::6237r1HE81V01lFx' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/push-notifications',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@pushNotifications',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@pushNotifications',
        'as' => 'api.generated::6237r1HE81V01lFx',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::GXbqls8dW1Xc96H6' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/push-notifications/read/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@pushNotificationsRead',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@pushNotificationsRead',
        'as' => 'api.generated::GXbqls8dW1Xc96H6',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::T5U6feHKmLyjFepn' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/twofactor',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@show2faForm',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@show2faForm',
        'as' => 'api.generated::T5U6feHKmLyjFepn',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::DujoNotPDulC2YYV' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/twofactor/enable',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@create2fa',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@create2fa',
        'as' => 'api.generated::DujoNotPDulC2YYV',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::ZQ0YY60faTet5XMV' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/twofactor/disable',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@disable2fa',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@disable2fa',
        'as' => 'api.generated::ZQ0YY60faTet5XMV',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::d4WEhvravBp4rAoC' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/delete-account',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\UserController@deleteAccount',
        'controller' => 'App\\Http\\Controllers\\Api\\UserController@deleteAccount',
        'as' => 'api.generated::d4WEhvravBp4rAoC',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::vwlUZYCnw7YGjrj2' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/deposit/methods',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\PaymentController@methods',
        'controller' => 'App\\Http\\Controllers\\Api\\PaymentController@methods',
        'as' => 'api.generated::vwlUZYCnw7YGjrj2',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::PtAEvs0FdWDEd82S' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/deposit/insert',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\PaymentController@depositInsert',
        'controller' => 'App\\Http\\Controllers\\Api\\PaymentController@depositInsert',
        'as' => 'api.generated::PtAEvs0FdWDEd82S',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::nkaLF8goOBhpZTvL' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/ticket',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\TicketController@supportTicket',
        'controller' => 'App\\Http\\Controllers\\Api\\TicketController@supportTicket',
        'as' => 'api.generated::nkaLF8goOBhpZTvL',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => 'api/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::DUaXwqGcpwiz3W8p' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/ticket/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\TicketController@storeSupportTicket',
        'controller' => 'App\\Http\\Controllers\\Api\\TicketController@storeSupportTicket',
        'as' => 'api.generated::DUaXwqGcpwiz3W8p',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => 'api/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::HqLtH1GwuXEHBN1Q' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/ticket/view/{ticket}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\TicketController@viewTicket',
        'controller' => 'App\\Http\\Controllers\\Api\\TicketController@viewTicket',
        'as' => 'api.generated::HqLtH1GwuXEHBN1Q',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => 'api/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::LBi1bgNgpT5fZdzN' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/ticket/reply/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\TicketController@replyTicket',
        'controller' => 'App\\Http\\Controllers\\Api\\TicketController@replyTicket',
        'as' => 'api.generated::LBi1bgNgpT5fZdzN',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => 'api/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::cuUFe1HZhuWDV9v6' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'api/ticket/close/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\TicketController@closeTicket',
        'controller' => 'App\\Http\\Controllers\\Api\\TicketController@closeTicket',
        'as' => 'api.generated::cuUFe1HZhuWDV9v6',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => 'api/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::7QPZHGNgeh5qeTBC' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/ticket/download/{attachment_id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\TicketController@ticketDownload',
        'controller' => 'App\\Http\\Controllers\\Api\\TicketController@ticketDownload',
        'as' => 'api.generated::7QPZHGNgeh5qeTBC',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => 'api/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'api.generated::ZTWmghpzbCzpXiVV' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/logout',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'maintenance',
          2 => 'auth:sanctum',
        ),
        'uses' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@logout',
        'controller' => 'App\\Http\\Controllers\\Api\\Auth\\LoginController@logout',
        'as' => 'api.generated::ZTWmghpzbCzpXiVV',
        'namespace' => 'App\\Http\\Controllers\\Api',
        'prefix' => '/api',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.login' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm',
        'as' => 'admin.login',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\LoginController@login',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\LoginController@login',
        'as' => 'admin.',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.logout' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/logout',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
          2 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\LoginController@logout',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\LoginController@logout',
        'as' => 'admin.logout',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
        'excluded_middleware' => 
        array (
          0 => 'admin.guest',
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password.reset' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/password/reset',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@showLinkRequestForm',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@showLinkRequestForm',
        'as' => 'admin.password.reset',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => 'admin/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password.' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/password/reset',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@sendResetCodeEmail',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@sendResetCodeEmail',
        'as' => 'admin.password.',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => 'admin/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password.code.verify' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/password/code-verify',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@codeVerify',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@codeVerify',
        'as' => 'admin.password.code.verify',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => 'admin/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password.verify.code' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/password/verify-code',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@verifyCode',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\ForgotPasswordController@verifyCode',
        'as' => 'admin.password.verify.code',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => 'admin/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password.reset.form' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/password/reset/{token}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\ResetPasswordController@showResetForm',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\ResetPasswordController@showResetForm',
        'as' => 'admin.password.reset.form',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password.change' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/password/reset/change',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin.guest',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\Auth\\ResetPasswordController@reset',
        'controller' => 'App\\Http\\Controllers\\Admin\\Auth\\ResetPasswordController@reset',
        'as' => 'admin.password.change',
        'namespace' => 'App\\Http\\Controllers\\Admin\\Auth',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.dashboard' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/dashboard',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@dashboard',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@dashboard',
        'as' => 'admin.dashboard',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.payment.chart' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/payment-chart',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@paymentReport',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@paymentReport',
        'as' => 'admin.payment.chart',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.profile' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/profile',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@profile',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@profile',
        'as' => 'admin.profile',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.profile.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/profile',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@profileUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@profileUpdate',
        'as' => 'admin.profile.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@password',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@password',
        'as' => 'admin.password',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.password.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@passwordUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@passwordUpdate',
        'as' => 'admin.password.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.notifications' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notifications',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@notifications',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@notifications',
        'as' => 'admin.notifications',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.notification.read' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/read/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@notificationRead',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@notificationRead',
        'as' => 'admin.notification.read',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.notifications.read.all' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notifications/read-all',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@readAllNotification',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@readAllNotification',
        'as' => 'admin.notifications.read.all',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.notifications.delete.all' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notifications/delete-all',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@deleteAllNotification',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@deleteAllNotification',
        'as' => 'admin.notifications.delete.all',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.notifications.delete.single' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notifications/delete-single/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@deleteSingleNotification',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@deleteSingleNotification',
        'as' => 'admin.notifications.delete.single',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.request.report' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/request-report',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@requestReport',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@requestReport',
        'as' => 'admin.request.report',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::9Va0tFxGyW686Qmo' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/request-report',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@reportSubmit',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@reportSubmit',
        'as' => 'admin.generated::9Va0tFxGyW686Qmo',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.download.attachment' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/download-attachments/{file_hash}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AdminController@downloadAttachment',
        'controller' => 'App\\Http\\Controllers\\Admin\\AdminController@downloadAttachment',
        'as' => 'admin.download.attachment',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.pickup.locations' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/bus/pickup/locations',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@pickupLocations',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@pickupLocations',
        'as' => 'admin.bus.pickup.locations',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/pickup',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.pickup.locations.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/bus/pickup/locations/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@storePickupLocation',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@storePickupLocation',
        'as' => 'admin.bus.pickup.locations.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/pickup',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.pickup.locations.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/bus/pickup/locations/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@togglePickupLocationStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@togglePickupLocationStatus',
        'as' => 'admin.bus.pickup.locations.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/pickup',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.pickup.locations.delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/bus/pickup/locations/delete/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@deletePickupLocation',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@deletePickupLocation',
        'as' => 'admin.bus.pickup.locations.delete',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/pickup',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.drop.locations' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/bus/drop/locations',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@dropLocations',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@dropLocations',
        'as' => 'admin.bus.drop.locations',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/drop',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.drop.locations.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/bus/drop/locations/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@storeDropLocation',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@storeDropLocation',
        'as' => 'admin.bus.drop.locations.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/drop',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.drop.locations.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/bus/drop/locations/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@toggleDropLocationStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@toggleDropLocationStatus',
        'as' => 'admin.bus.drop.locations.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/drop',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.bus.drop.locations.delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/bus/drop/locations/delete/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LocationController@deleteDropLocation',
        'controller' => 'App\\Http\\Controllers\\Admin\\LocationController@deleteDropLocation',
        'as' => 'admin.bus.drop.locations.delete',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/bus/drop',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.counter.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/counter',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\CounterController@counters',
        'controller' => 'App\\Http\\Controllers\\Admin\\CounterController@counters',
        'as' => 'admin.counter.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/counter',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.counter.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/counter/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\CounterController@counterStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\CounterController@counterStore',
        'as' => 'admin.counter.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/counter',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.counter.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/counter/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\CounterController@status',
        'controller' => 'App\\Http\\Controllers\\Admin\\CounterController@status',
        'as' => 'admin.counter.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/counter',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.layouts.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/fleet/layouts',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@layout',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@layout',
        'as' => 'admin.fleet.layouts.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/layouts',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.layouts.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/fleet/layouts/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@layoutStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@layoutStore',
        'as' => 'admin.fleet.layouts.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/layouts',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.layouts.remove' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/fleet/layouts/remove/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@removeLayout',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@removeLayout',
        'as' => 'admin.fleet.layouts.remove',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/layouts',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.type.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/fleet/type',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@type',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@type',
        'as' => 'admin.fleet.type.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/type',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.type.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/fleet/type/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@typeStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@typeStore',
        'as' => 'admin.fleet.type.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/type',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.type.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/fleet/type/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@typeStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@typeStatus',
        'as' => 'admin.fleet.type.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/type',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.vehicles.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/fleet/vehicles',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@vehicles',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@vehicles',
        'as' => 'admin.fleet.vehicles.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/vehicles',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.vehicles.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/fleet/vehicles/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@vehiclesStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@vehiclesStore',
        'as' => 'admin.fleet.vehicles.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/vehicles',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.fleet.vehicles.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/fleet/vehicles/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@vehicleStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageFleetController@vehicleStatus',
        'as' => 'admin.fleet.vehicles.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/fleet/vehicles',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.list' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/manage/trip',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@trips',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@trips',
        'as' => 'admin.trip.list',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/trip',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/trip/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@tripStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@tripStore',
        'as' => 'admin.trip.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/trip',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/trip/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@tripStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@tripStatus',
        'as' => 'admin.trip.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/trip',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.route.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/manage/route',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeList',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeList',
        'as' => 'admin.trip.route.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/route',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.route.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/manage/route/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeCreate',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeCreate',
        'as' => 'admin.trip.route.create',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/route',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.route.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/route/store',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeStore',
        'as' => 'admin.trip.route.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/route',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.route.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/manage/route/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeEdit',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeEdit',
        'as' => 'admin.trip.route.edit',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/route',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.route.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/route/update/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeUpdate',
        'as' => 'admin.trip.route.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/route',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.route.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/route/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@routeStatus',
        'as' => 'admin.trip.route.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/route',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.schedule.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/manage/schedule',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@schedules',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@schedules',
        'as' => 'admin.trip.schedule.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/schedule',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.schedule.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/schedule/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@scheduleStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@scheduleStore',
        'as' => 'admin.trip.schedule.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/schedule',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.schedule.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/schedule/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@scheduleStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@scheduleStatus',
        'as' => 'admin.trip.schedule.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/schedule',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.vehicle.assign.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/manage/assigned-vehicle',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@assignedVehicleLists',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@assignedVehicleLists',
        'as' => 'admin.trip.vehicle.assign.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/assigned-vehicle',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.vehicle.assign.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/assigned-vehicle/store/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@assignVehicle',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@assignVehicle',
        'as' => 'admin.trip.vehicle.assign.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/assigned-vehicle',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.trip.vehicle.assign.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/manage/assigned-vehicle/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageTripController@assignedVehicleStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageTripController@assignedVehicleStatus',
        'as' => 'admin.trip.vehicle.assign.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/manage/assigned-vehicle',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.all' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@allUsers',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@allUsers',
        'as' => 'admin.users.all',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.active' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/active',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@activeUsers',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@activeUsers',
        'as' => 'admin.users.active',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.banned' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/banned',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@bannedUsers',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@bannedUsers',
        'as' => 'admin.users.banned',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.email.verified' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/email-verified',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@emailVerifiedUsers',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@emailVerifiedUsers',
        'as' => 'admin.users.email.verified',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.email.unverified' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/email-unverified',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@emailUnverifiedUsers',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@emailUnverifiedUsers',
        'as' => 'admin.users.email.unverified',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.mobile.unverified' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/mobile-unverified',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@mobileUnverifiedUsers',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@mobileUnverifiedUsers',
        'as' => 'admin.users.mobile.unverified',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.mobile.verified' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/mobile-verified',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@mobileVerifiedUsers',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@mobileVerifiedUsers',
        'as' => 'admin.users.mobile.verified',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.detail' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/detail/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@detail',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@detail',
        'as' => 'admin.users.detail',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/users/update/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@update',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@update',
        'as' => 'admin.users.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.add.sub.balance' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/users/add-sub-balance/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@addSubBalance',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@addSubBalance',
        'as' => 'admin.users.add.sub.balance',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.notification.single' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/send-notification/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@showNotificationSingleForm',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@showNotificationSingleForm',
        'as' => 'admin.users.notification.single',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/users/send-notification/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@sendNotificationSingle',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@sendNotificationSingle',
        'as' => 'admin.users.',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.login' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/login/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@login',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@login',
        'as' => 'admin.users.login',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/users/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@status',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@status',
        'as' => 'admin.users.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.notification.all' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/send-notification',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@showNotificationAllForm',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@showNotificationAllForm',
        'as' => 'admin.users.notification.all',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.notification.all.send' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/users/send-notification',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@sendNotificationAll',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@sendNotificationAll',
        'as' => 'admin.users.notification.all.send',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.list' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/list',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@list',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@list',
        'as' => 'admin.users.list',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.segment.count' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/count-by-segment/{methodName}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@countBySegment',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@countBySegment',
        'as' => 'admin.users.segment.count',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.users.notification.log' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/users/notification-log/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@notificationLog',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManageUsersController@notificationLog',
        'as' => 'admin.users.notification.log',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/users',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.automatic.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/gateway/automatic',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@index',
        'controller' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@index',
        'as' => 'admin.gateway.automatic.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/automatic',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.automatic.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/gateway/automatic/edit/{alias}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@edit',
        'controller' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@edit',
        'as' => 'admin.gateway.automatic.edit',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/automatic',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.automatic.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/gateway/automatic/update/{code}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@update',
        'controller' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@update',
        'as' => 'admin.gateway.automatic.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/automatic',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.automatic.remove' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/gateway/automatic/remove/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@remove',
        'controller' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@remove',
        'as' => 'admin.gateway.automatic.remove',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/automatic',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.automatic.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/gateway/automatic/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@status',
        'controller' => 'App\\Http\\Controllers\\Admin\\AutomaticGatewayController@status',
        'as' => 'admin.gateway.automatic.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/automatic',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.manual.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/gateway/manual',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@index',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@index',
        'as' => 'admin.gateway.manual.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/manual',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.manual.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/gateway/manual/new',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@create',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@create',
        'as' => 'admin.gateway.manual.create',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/manual',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.manual.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/gateway/manual/new',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@store',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@store',
        'as' => 'admin.gateway.manual.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/manual',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.manual.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/gateway/manual/edit/{alias}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@edit',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@edit',
        'as' => 'admin.gateway.manual.edit',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/manual',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.manual.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/gateway/manual/update/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@update',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@update',
        'as' => 'admin.gateway.manual.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/manual',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.gateway.manual.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/gateway/manual/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@status',
        'controller' => 'App\\Http\\Controllers\\Admin\\ManualGatewayController@status',
        'as' => 'admin.gateway.manual.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/gateway/manual',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.list' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/deposit/all/{user_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@deposit',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@deposit',
        'as' => 'admin.deposit.list',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.pending' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/deposit/pending/{user_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@pending',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@pending',
        'as' => 'admin.deposit.pending',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.rejected' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/deposit/rejected/{user_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@rejected',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@rejected',
        'as' => 'admin.deposit.rejected',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.approved' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/deposit/approved/{user_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@approved',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@approved',
        'as' => 'admin.deposit.approved',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.successful' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/deposit/successful/{user_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@successful',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@successful',
        'as' => 'admin.deposit.successful',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.initiated' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/deposit/initiated/{user_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@initiated',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@initiated',
        'as' => 'admin.deposit.initiated',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.details' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/deposit/details/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@details',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@details',
        'as' => 'admin.deposit.details',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.reject' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/deposit/reject',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@reject',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@reject',
        'as' => 'admin.deposit.reject',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.deposit.approve' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/deposit/approve/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\DepositController@approve',
        'controller' => 'App\\Http\\Controllers\\Admin\\DepositController@approve',
        'as' => 'admin.deposit.approve',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/deposit',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.report.transaction' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/report/transaction/{user_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ReportController@transaction',
        'controller' => 'App\\Http\\Controllers\\Admin\\ReportController@transaction',
        'as' => 'admin.report.transaction',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/report',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.report.login.history' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/report/login/history',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ReportController@loginHistory',
        'controller' => 'App\\Http\\Controllers\\Admin\\ReportController@loginHistory',
        'as' => 'admin.report.login.history',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/report',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.report.login.ipHistory' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/report/login/ipHistory/{ip}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ReportController@loginIpHistory',
        'controller' => 'App\\Http\\Controllers\\Admin\\ReportController@loginIpHistory',
        'as' => 'admin.report.login.ipHistory',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/report',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.report.notification.history' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/report/notification/history',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ReportController@notificationHistory',
        'controller' => 'App\\Http\\Controllers\\Admin\\ReportController@notificationHistory',
        'as' => 'admin.report.notification.history',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/report',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.report.email.details' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/report/email/detail/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ReportController@emailDetails',
        'controller' => 'App\\Http\\Controllers\\Admin\\ReportController@emailDetails',
        'as' => 'admin.report.email.details',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/report',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/ticket',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@tickets',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@tickets',
        'as' => 'admin.ticket.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.pending' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/ticket/pending',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@pendingTicket',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@pendingTicket',
        'as' => 'admin.ticket.pending',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.closed' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/ticket/closed',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@closedTicket',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@closedTicket',
        'as' => 'admin.ticket.closed',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.answered' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/ticket/answered',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@answeredTicket',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@answeredTicket',
        'as' => 'admin.ticket.answered',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/ticket/view/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@ticketReply',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@ticketReply',
        'as' => 'admin.ticket.view',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.reply' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/ticket/reply/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@replyTicket',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@replyTicket',
        'as' => 'admin.ticket.reply',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.close' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/ticket/close/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@closeTicket',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@closeTicket',
        'as' => 'admin.ticket.close',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/ticket/download/{attachment_id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@ticketDownload',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@ticketDownload',
        'as' => 'admin.ticket.download',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.ticket.delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/ticket/delete/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@ticketDelete',
        'controller' => 'App\\Http\\Controllers\\Admin\\SupportTicketController@ticketDelete',
        'as' => 'admin.ticket.delete',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.manage' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/language',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@langManage',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@langManage',
        'as' => 'admin.language.manage',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.manage.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/language',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@langStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@langStore',
        'as' => 'admin.language.manage.store',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.manage.delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/language/delete/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@langDelete',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@langDelete',
        'as' => 'admin.language.manage.delete',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.manage.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/language/update/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@langUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@langUpdate',
        'as' => 'admin.language.manage.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.key' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/language/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@langEdit',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@langEdit',
        'as' => 'admin.language.key',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.import.lang' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/language/import',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@langImport',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@langImport',
        'as' => 'admin.language.import.lang',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.store.key' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/language/store/key/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@storeLanguageJson',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@storeLanguageJson',
        'as' => 'admin.language.store.key',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.delete.key' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/language/delete/key/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@deleteLanguageJson',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@deleteLanguageJson',
        'as' => 'admin.language.delete.key',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.update.key' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/language/update/key/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@updateLanguageJson',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@updateLanguageJson',
        'as' => 'admin.language.update.key',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.language.get.key' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/language/get-keys',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\LanguageController@getKeys',
        'controller' => 'App\\Http\\Controllers\\Admin\\LanguageController@getKeys',
        'as' => 'admin.language.get.key',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/language',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.system' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/system-setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@systemSetting',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@systemSetting',
        'as' => 'admin.setting.system',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.general' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/general-setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@general',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@general',
        'as' => 'admin.setting.general',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::bWVNrtG5bqVOikW9' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/general-setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@generalUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@generalUpdate',
        'as' => 'admin.generated::bWVNrtG5bqVOikW9',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.socialite.credentials' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/setting/social/credentials',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@socialiteCredentials',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@socialiteCredentials',
        'as' => 'admin.setting.socialite.credentials',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.socialite.credentials.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/setting/social/credentials/update/{key}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@updateSocialiteCredential',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@updateSocialiteCredential',
        'as' => 'admin.setting.socialite.credentials.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.socialite.credentials.status.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/setting/social/credentials/status/{key}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@updateSocialiteCredentialStatus',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@updateSocialiteCredentialStatus',
        'as' => 'admin.setting.socialite.credentials.status.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.system.configuration' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/setting/system-configuration',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@systemConfiguration',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@systemConfiguration',
        'as' => 'admin.setting.system.configuration',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::fyuH8y6IvBy3hNWt' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/setting/system-configuration',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@systemConfigurationSubmit',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@systemConfigurationSubmit',
        'as' => 'admin.generated::fyuH8y6IvBy3hNWt',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.logo.icon' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/setting/logo-icon',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@logoIcon',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@logoIcon',
        'as' => 'admin.setting.logo.icon',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::AU0IAmUU6Dm0AY6x' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/setting/logo-icon',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@logoIconUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@logoIconUpdate',
        'as' => 'admin.generated::AU0IAmUU6Dm0AY6x',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.custom.css' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/custom-css',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@customCss',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@customCss',
        'as' => 'admin.setting.custom.css',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::f0k2rwKWMNaYWnAU' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/custom-css',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@customCssSubmit',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@customCssSubmit',
        'as' => 'admin.generated::f0k2rwKWMNaYWnAU',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.sitemap' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sitemap',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@sitemap',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@sitemap',
        'as' => 'admin.setting.sitemap',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::pJNtW36YG2lv69jm' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/sitemap',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@sitemapSubmit',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@sitemapSubmit',
        'as' => 'admin.generated::pJNtW36YG2lv69jm',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.robot' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/robot',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@robot',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@robot',
        'as' => 'admin.setting.robot',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::XGi0J4PJvRCaIRio' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/robot',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@robotSubmit',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@robotSubmit',
        'as' => 'admin.generated::XGi0J4PJvRCaIRio',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.search.form' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/search-form-settings',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@searchFormSettings',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@searchFormSettings',
        'as' => 'admin.setting.search.form',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::puOgJELfj2fn6r8j' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/search-form-settings',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@searchFormSettingsUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@searchFormSettingsUpdate',
        'as' => 'admin.generated::puOgJELfj2fn6r8j',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.cookie' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/cookie',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@cookie',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@cookie',
        'as' => 'admin.setting.cookie',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::Vp7UmC9c958qNHcA' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/cookie',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@cookieSubmit',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@cookieSubmit',
        'as' => 'admin.generated::Vp7UmC9c958qNHcA',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.maintenance.mode' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/maintenance-mode',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@maintenanceMode',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@maintenanceMode',
        'as' => 'admin.maintenance.mode',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.generated::iyx9yuppQkCRUMO6' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/maintenance-mode',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@maintenanceModeSubmit',
        'controller' => 'App\\Http\\Controllers\\Admin\\GeneralSettingController@maintenanceModeSubmit',
        'as' => 'admin.generated::iyx9yuppQkCRUMO6',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.global.email' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/global/email',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalEmail',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalEmail',
        'as' => 'admin.setting.notification.global.email',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.global.email.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/global/email/update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalEmailUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalEmailUpdate',
        'as' => 'admin.setting.notification.global.email.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.global.sms' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/global/sms',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalSms',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalSms',
        'as' => 'admin.setting.notification.global.sms',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.global.sms.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/global/sms/update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalSmsUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalSmsUpdate',
        'as' => 'admin.setting.notification.global.sms.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.global.push' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/global/push',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalPush',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalPush',
        'as' => 'admin.setting.notification.global.push',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.global.push.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/global/push/update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalPushUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@globalPushUpdate',
        'as' => 'admin.setting.notification.global.push.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.templates' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/templates',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@templates',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@templates',
        'as' => 'admin.setting.notification.templates',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.template.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/template/edit/{type}/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@templateEdit',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@templateEdit',
        'as' => 'admin.setting.notification.template.edit',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.template.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/template/update/{type}/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate',
        'as' => 'admin.setting.notification.template.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.email' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/email/setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@emailSetting',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@emailSetting',
        'as' => 'admin.setting.notification.email',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/email/setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@emailSettingUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@emailSettingUpdate',
        'as' => 'admin.setting.notification.',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.email.test' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/email/test',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@emailTest',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@emailTest',
        'as' => 'admin.setting.notification.email.test',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.sms' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/sms/setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@smsSetting',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@smsSetting',
        'as' => 'admin.setting.notification.sms',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.generated::MU0GyEF4IUeRVHwe' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/sms/setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@smsSettingUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@smsSettingUpdate',
        'as' => 'admin.setting.notification.generated::MU0GyEF4IUeRVHwe',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.sms.test' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/sms/test',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@smsTest',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@smsTest',
        'as' => 'admin.setting.notification.sms.test',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.push' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/notification/push/setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSetting',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSetting',
        'as' => 'admin.setting.notification.push',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.generated::y6NULpTyDJeEVgsp' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/notification/push/setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSettingUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSettingUpdate',
        'as' => 'admin.setting.notification.generated::y6NULpTyDJeEVgsp',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.push.upload' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/notification/notification/push/setting/upload',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSettingUpload',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSettingUpload',
        'as' => 'admin.setting.notification.push.upload',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.setting.notification.push.download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/notification/notification/push/setting/download',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSettingDownload',
        'controller' => 'App\\Http\\Controllers\\Admin\\NotificationController@pushSettingDownload',
        'as' => 'admin.setting.notification.push.download',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/notification',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.extensions.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/extensions',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ExtensionController@index',
        'controller' => 'App\\Http\\Controllers\\Admin\\ExtensionController@index',
        'as' => 'admin.extensions.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/extensions',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.extensions.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/extensions/update/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ExtensionController@update',
        'controller' => 'App\\Http\\Controllers\\Admin\\ExtensionController@update',
        'as' => 'admin.extensions.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/extensions',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.extensions.status' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/extensions/status/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\ExtensionController@status',
        'controller' => 'App\\Http\\Controllers\\Admin\\ExtensionController@status',
        'as' => 'admin.extensions.status',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/extensions',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.system.info' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/system/info',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SystemController@systemInfo',
        'controller' => 'App\\Http\\Controllers\\Admin\\SystemController@systemInfo',
        'as' => 'admin.system.info',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/system',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.system.server.info' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/system/server-info',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SystemController@systemServerInfo',
        'controller' => 'App\\Http\\Controllers\\Admin\\SystemController@systemServerInfo',
        'as' => 'admin.system.server.info',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/system',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.system.optimize' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/system/optimize',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SystemController@optimize',
        'controller' => 'App\\Http\\Controllers\\Admin\\SystemController@optimize',
        'as' => 'admin.system.optimize',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/system',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.system.optimize.clear' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/system/optimize-clear',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SystemController@optimizeClear',
        'controller' => 'App\\Http\\Controllers\\Admin\\SystemController@optimizeClear',
        'as' => 'admin.system.optimize.clear',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/system',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.system.update' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/system/system-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SystemController@systemUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\SystemController@systemUpdate',
        'as' => 'admin.system.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/system',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.system.update.process' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/system/system-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SystemController@systemUpdateProcess',
        'controller' => 'App\\Http\\Controllers\\Admin\\SystemController@systemUpdateProcess',
        'as' => 'admin.system.update.process',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/system',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.system.update.log' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/system/system-update/log',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\SystemController@systemUpdateLog',
        'controller' => 'App\\Http\\Controllers\\Admin\\SystemController@systemUpdateLog',
        'as' => 'admin.system.update.log',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/system',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.seo' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/seo',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@seoEdit',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@seoEdit',
        'as' => 'admin.seo',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => '/admin',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/index',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@index',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@index',
        'as' => 'admin.frontend.index',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.templates' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/templates',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@templates',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@templates',
        'as' => 'admin.frontend.templates',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.templates.active' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/templates',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@templatesActive',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@templatesActive',
        'as' => 'admin.frontend.templates.active',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.sections' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/frontend-sections/{key?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendSections',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendSections',
        'as' => 'admin.frontend.sections',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.sections.content' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/frontend-content/{key}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendContent',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendContent',
        'as' => 'admin.frontend.sections.content',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.sections.element' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/frontend-element/{key}/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendElement',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendElement',
        'as' => 'admin.frontend.sections.element',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.sections.element.slug.check' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/frontend-slug-check/{key}/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendElementSlugCheck',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendElementSlugCheck',
        'as' => 'admin.frontend.sections.element.slug.check',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.sections.element.seo' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/frontend-element-seo/{key}/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendSeo',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendSeo',
        'as' => 'admin.frontend.sections.element.seo',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/frontend-element-seo/{key}/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendSeoUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@frontendSeoUpdate',
        'as' => 'admin.frontend.',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.remove' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/remove/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\FrontendController@remove',
        'controller' => 'App\\Http\\Controllers\\Admin\\FrontendController@remove',
        'as' => 'admin.frontend.remove',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.pages' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/manage-pages',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePages',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePages',
        'as' => 'admin.frontend.manage.pages',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.pages.check.slug' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/manage-pages/check-slug/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@checkSlug',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@checkSlug',
        'as' => 'admin.frontend.manage.pages.check.slug',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.pages.save' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/manage-pages',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePagesSave',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePagesSave',
        'as' => 'admin.frontend.manage.pages.save',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.pages.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/manage-pages/update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePagesUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePagesUpdate',
        'as' => 'admin.frontend.manage.pages.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.pages.delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/manage-pages/delete/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePagesDelete',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@managePagesDelete',
        'as' => 'admin.frontend.manage.pages.delete',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.section' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/manage-section/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSection',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSection',
        'as' => 'admin.frontend.manage.section',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.section.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/manage-section/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSectionUpdate',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSectionUpdate',
        'as' => 'admin.frontend.manage.section.update',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.manage.pages.seo' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/frontend/manage-seo/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSeo',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSeo',
        'as' => 'admin.frontend.manage.pages.seo',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.frontend.generated::ZDsa49HsBNMrIPgg' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/frontend/manage-seo/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin',
        ),
        'uses' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSeoStore',
        'controller' => 'App\\Http\\Controllers\\Admin\\PageBuilderController@manageSeoStore',
        'as' => 'admin.frontend.generated::ZDsa49HsBNMrIPgg',
        'namespace' => 'App\\Http\\Controllers\\Admin',
        'prefix' => 'admin/frontend',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Paypal' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/paypal',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Paypal\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Paypal\\ProcessController@ipn',
        'as' => 'ipn.Paypal',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.PaypalSdk' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ipn/paypal-sdk',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PaypalSdk\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PaypalSdk\\ProcessController@ipn',
        'as' => 'ipn.PaypalSdk',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.PerfectMoney' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/perfect-money',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PerfectMoney\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PerfectMoney\\ProcessController@ipn',
        'as' => 'ipn.PerfectMoney',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Stripe' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/stripe',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Stripe\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Stripe\\ProcessController@ipn',
        'as' => 'ipn.Stripe',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.StripeJs' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/stripe-js',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\StripeJs\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\StripeJs\\ProcessController@ipn',
        'as' => 'ipn.StripeJs',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.StripeV3' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/stripe-v3',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\StripeV3\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\StripeV3\\ProcessController@ipn',
        'as' => 'ipn.StripeV3',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Skrill' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/skrill',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Skrill\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Skrill\\ProcessController@ipn',
        'as' => 'ipn.Skrill',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Paytm' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/paytm',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Paytm\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Paytm\\ProcessController@ipn',
        'as' => 'ipn.Paytm',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Payeer' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/payeer',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Payeer\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Payeer\\ProcessController@ipn',
        'as' => 'ipn.Payeer',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Paystack' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/paystack',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Paystack\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Paystack\\ProcessController@ipn',
        'as' => 'ipn.Paystack',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Flutterwave' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ipn/flutterwave/{trx}/{type}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Flutterwave\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Flutterwave\\ProcessController@ipn',
        'as' => 'ipn.Flutterwave',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Razorpay' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/razorpay',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Razorpay\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Razorpay\\ProcessController@ipn',
        'as' => 'ipn.Razorpay',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Instamojo' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/instamojo',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Instamojo\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Instamojo\\ProcessController@ipn',
        'as' => 'ipn.Instamojo',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Blockchain' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ipn/blockchain',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Blockchain\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Blockchain\\ProcessController@ipn',
        'as' => 'ipn.Blockchain',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Coinpayments' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/coinpayments',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Coinpayments\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Coinpayments\\ProcessController@ipn',
        'as' => 'ipn.Coinpayments',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.CoinpaymentsFiat' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/coinpayments-fiat',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\CoinpaymentsFiat\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\CoinpaymentsFiat\\ProcessController@ipn',
        'as' => 'ipn.CoinpaymentsFiat',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Coingate' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/coingate',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Coingate\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Coingate\\ProcessController@ipn',
        'as' => 'ipn.Coingate',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.CoinbaseCommerce' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/coinbase-commerce',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\CoinbaseCommerce\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\CoinbaseCommerce\\ProcessController@ipn',
        'as' => 'ipn.CoinbaseCommerce',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Mollie' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ipn/mollie',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Mollie\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Mollie\\ProcessController@ipn',
        'as' => 'ipn.Mollie',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Cashmaal' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/cashmaal',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Cashmaal\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Cashmaal\\ProcessController@ipn',
        'as' => 'ipn.Cashmaal',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.MercadoPago' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/mercado-pago',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\MercadoPago\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\MercadoPago\\ProcessController@ipn',
        'as' => 'ipn.MercadoPago',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Authorize' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/authorize',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Authorize\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Authorize\\ProcessController@ipn',
        'as' => 'ipn.Authorize',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.NMI' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ipn/nmi',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\NMI\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\NMI\\ProcessController@ipn',
        'as' => 'ipn.NMI',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.BTCPay' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
        2 => 'POST',
        3 => 'PUT',
        4 => 'PATCH',
        5 => 'DELETE',
        6 => 'OPTIONS',
      ),
      'uri' => 'ipn/btc-pay',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\BTCPay\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\BTCPay\\ProcessController@ipn',
        'as' => 'ipn.BTCPay',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.NowPaymentsHosted' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/now-payments-hosted',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\NowPaymentsHosted\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\NowPaymentsHosted\\ProcessController@ipn',
        'as' => 'ipn.NowPaymentsHosted',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.NowPaymentsCheckout' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/now-payments-checkout',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\NowPaymentsCheckout\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\NowPaymentsCheckout\\ProcessController@ipn',
        'as' => 'ipn.NowPaymentsCheckout',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.TwoCheckout' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/2checkout',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\TwoCheckout\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\TwoCheckout\\ProcessController@ipn',
        'as' => 'ipn.TwoCheckout',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Checkout' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
        2 => 'POST',
        3 => 'PUT',
        4 => 'PATCH',
        5 => 'DELETE',
        6 => 'OPTIONS',
      ),
      'uri' => 'ipn/checkout',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Checkout\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Checkout\\ProcessController@ipn',
        'as' => 'ipn.Checkout',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.SslCommerz' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/sslcommerz',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\SslCommerz\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\SslCommerz\\ProcessController@ipn',
        'as' => 'ipn.SslCommerz',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Aamarpay' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ipn/aamarpay',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Aamarpay\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Aamarpay\\ProcessController@ipn',
        'as' => 'ipn.Aamarpay',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ipn.Binance' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ipn/binance',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\Binance\\ProcessController@ipn',
        'controller' => 'App\\Http\\Controllers\\Gateway\\Binance\\ProcessController@ipn',
        'as' => 'ipn.Binance',
        'namespace' => 'App\\Http\\Controllers\\Gateway',
        'prefix' => '/ipn',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.login' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/login',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\LoginController@showLoginForm',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\LoginController@showLoginForm',
        'as' => 'user.login',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/login',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\LoginController@login',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\LoginController@login',
        'as' => 'user.',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.logout' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/logout',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
          3 => 'auth',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\LoginController@logout',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\LoginController@logout',
        'as' => 'user.logout',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
        'excluded_middleware' => 
        array (
          0 => 'guest',
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.register' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/register',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
          3 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\RegisterController@showRegistrationForm',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\RegisterController@showRegistrationForm',
        'as' => 'user.register',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.generated::j21KbmSmEyrBpDhy' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/register',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
          3 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\RegisterController@register',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\RegisterController@register',
        'as' => 'user.generated::j21KbmSmEyrBpDhy',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.checkUser' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/check-user',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
          3 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\RegisterController@checkUser',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\RegisterController@checkUser',
        'as' => 'user.checkUser',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
        'excluded_middleware' => 
        array (
          0 => 'guest',
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.password.request' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/password/reset',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@showLinkRequestForm',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@showLinkRequestForm',
        'as' => 'user.password.request',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => 'user/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.password.email' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/password/email',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@sendResetCodeEmail',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@sendResetCodeEmail',
        'as' => 'user.password.email',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => 'user/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.password.code.verify' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/password/code-verify',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@codeVerify',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@codeVerify',
        'as' => 'user.password.code.verify',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => 'user/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.password.verify.code' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/password/verify-code',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@verifyCode',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\ForgotPasswordController@verifyCode',
        'as' => 'user.password.verify.code',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => 'user/password',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.password.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/password/reset',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\ResetPasswordController@reset',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\ResetPasswordController@reset',
        'as' => 'user.password.update',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.password.reset' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/password/reset/{token}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\ResetPasswordController@showResetForm',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\ResetPasswordController@showResetForm',
        'as' => 'user.password.reset',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.social.login' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/social-login/{provider}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\SocialiteController@socialLogin',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\SocialiteController@socialLogin',
        'as' => 'user.social.login',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.social.login.callback' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/social-login/callback/{provider}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'guest',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\Auth\\SocialiteController@callback',
        'controller' => 'App\\Http\\Controllers\\User\\Auth\\SocialiteController@callback',
        'as' => 'user.social.login.callback',
        'namespace' => 'App\\Http\\Controllers\\User\\Auth',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.data' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/user-data',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@userData',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@userData',
        'as' => 'user.data',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.data.submit' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/user-data-submit',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@userDataSubmit',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@userDataSubmit',
        'as' => 'user.data.submit',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.authorization' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/authorization',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\AuthorizationController@authorizeForm',
        'controller' => 'App\\Http\\Controllers\\User\\AuthorizationController@authorizeForm',
        'as' => 'user.authorization',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.send.verify.code' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/resend-verify/{type}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\AuthorizationController@sendVerifyCode',
        'controller' => 'App\\Http\\Controllers\\User\\AuthorizationController@sendVerifyCode',
        'as' => 'user.send.verify.code',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.verify.email' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/verify-email',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\AuthorizationController@emailVerification',
        'controller' => 'App\\Http\\Controllers\\User\\AuthorizationController@emailVerification',
        'as' => 'user.verify.email',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.verify.mobile' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/verify-mobile',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\AuthorizationController@mobileVerification',
        'controller' => 'App\\Http\\Controllers\\User\\AuthorizationController@mobileVerification',
        'as' => 'user.verify.mobile',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.2fa.verify' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/verify-g2fa',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\AuthorizationController@g2faVerification',
        'controller' => 'App\\Http\\Controllers\\User\\AuthorizationController@g2faVerification',
        'as' => 'user.2fa.verify',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.home' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/dashboard',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@home',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@home',
        'as' => 'user.home',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.download.attachment' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/download-attachments/{file_hash}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@downloadAttachment',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@downloadAttachment',
        'as' => 'user.download.attachment',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.twofactor' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/twofactor',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@show2faForm',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@show2faForm',
        'as' => 'user.twofactor',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.twofactor.enable' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/twofactor/enable',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@create2fa',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@create2fa',
        'as' => 'user.twofactor.enable',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.twofactor.disable' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/twofactor/disable',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@disable2fa',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@disable2fa',
        'as' => 'user.twofactor.disable',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.ticket.history' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/booked-ticket/history',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@ticketHistory',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@ticketHistory',
        'as' => 'user.ticket.history',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.ticket.print' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/booked-ticket/print/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@printTicket',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@printTicket',
        'as' => 'user.ticket.print',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.deposit.history' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
        2 => 'POST',
        3 => 'PUT',
        4 => 'PATCH',
        5 => 'DELETE',
        6 => 'OPTIONS',
      ),
      'uri' => 'user/payment/history',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@depositHistory',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@depositHistory',
        'as' => 'user.deposit.history',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.transactions' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/transactions',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@transactions',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@transactions',
        'as' => 'user.transactions',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.add.device.token' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/add-device-token',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\UserController@addDeviceToken',
        'controller' => 'App\\Http\\Controllers\\User\\UserController@addDeviceToken',
        'as' => 'user.add.device.token',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.profile.setting' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/profile-setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\ProfileController@profile',
        'controller' => 'App\\Http\\Controllers\\User\\ProfileController@profile',
        'as' => 'user.profile.setting',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.generated::hoJJoJrxyPOqeo8C' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/profile-setting',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\ProfileController@submitProfile',
        'controller' => 'App\\Http\\Controllers\\User\\ProfileController@submitProfile',
        'as' => 'user.generated::hoJJoJrxyPOqeo8C',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.change.password' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/change-password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\ProfileController@changePassword',
        'controller' => 'App\\Http\\Controllers\\User\\ProfileController@changePassword',
        'as' => 'user.change.password',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.generated::XLOrQlMAs1iJSSGs' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/change-password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\User\\ProfileController@submitPassword',
        'controller' => 'App\\Http\\Controllers\\User\\ProfileController@submitPassword',
        'as' => 'user.generated::XLOrQlMAs1iJSSGs',
        'namespace' => 'App\\Http\\Controllers\\User',
        'prefix' => '/user',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.deposit.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
        2 => 'POST',
        3 => 'PUT',
        4 => 'PATCH',
        5 => 'DELETE',
        6 => 'OPTIONS',
      ),
      'uri' => 'user/payment',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PaymentController@deposit',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PaymentController@deposit',
        'as' => 'user.deposit.index',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => 'user/payment',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.deposit.insert' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/payment/insert',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PaymentController@depositInsert',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PaymentController@depositInsert',
        'as' => 'user.deposit.insert',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => 'user/payment',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.deposit.confirm' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/payment/confirm',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PaymentController@depositConfirm',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PaymentController@depositConfirm',
        'as' => 'user.deposit.confirm',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => 'user/payment',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.deposit.manual.confirm' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'user/payment/manual',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PaymentController@manualDepositConfirm',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PaymentController@manualDepositConfirm',
        'as' => 'user.deposit.manual.confirm',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => 'user/payment',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'user.deposit.manual.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'user/payment/manual',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
          2 => 'auth',
          3 => 'check.status',
          4 => 'registration.complete',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PaymentController@manualDepositUpdate',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PaymentController@manualDepositUpdate',
        'as' => 'user.deposit.manual.update',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => 'user/payment',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::Vp1phRGbWGL4EOoh' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'clear',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'O:55:"Laravel\\SerializableClosure\\UnsignedSerializableClosure":1:{s:12:"serializable";O:46:"Laravel\\SerializableClosure\\Serializers\\Native":5:{s:3:"use";a:0:{}s:8:"function";s:80:"function () {
    \\Illuminate\\Support\\Facades\\Artisan::call(\'optimize:clear\');
}";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"00000000000005ff0000000000000000";}}',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::Vp1phRGbWGL4EOoh',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ticket',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\TicketController@supportTicket',
        'controller' => 'App\\Http\\Controllers\\TicketController@supportTicket',
        'as' => 'ticket.index',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.open' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ticket/new',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\TicketController@openSupportTicket',
        'controller' => 'App\\Http\\Controllers\\TicketController@openSupportTicket',
        'as' => 'ticket.open',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ticket/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\TicketController@storeSupportTicket',
        'controller' => 'App\\Http\\Controllers\\TicketController@storeSupportTicket',
        'as' => 'ticket.store',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ticket/view/{ticket}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\TicketController@viewTicket',
        'controller' => 'App\\Http\\Controllers\\TicketController@viewTicket',
        'as' => 'ticket.view',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.reply' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ticket/reply/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\TicketController@replyTicket',
        'controller' => 'App\\Http\\Controllers\\TicketController@replyTicket',
        'as' => 'ticket.reply',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.close' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ticket/close/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\TicketController@closeTicket',
        'controller' => 'App\\Http\\Controllers\\TicketController@closeTicket',
        'as' => 'ticket.close',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ticket/download/{attachment_id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\TicketController@ticketDownload',
        'controller' => 'App\\Http\\Controllers\\TicketController@ticketDownload',
        'as' => 'ticket.download',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '/ticket',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'deposit.app.confirm' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'app/deposit/confirm/{hash}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\Gateway\\PaymentController@appDepositConfirm',
        'controller' => 'App\\Http\\Controllers\\Gateway\\PaymentController@appDepositConfirm',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'deposit.app.confirm',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'contact' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'contact',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@contact',
        'controller' => 'App\\Http\\Controllers\\SiteController@contact',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'contact',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::DlehXO0aS2BRuI7y' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'contact',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@contactSubmit',
        'controller' => 'App\\Http\\Controllers\\SiteController@contactSubmit',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::DlehXO0aS2BRuI7y',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'lang' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'change/{lang?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@changeLanguage',
        'controller' => 'App\\Http\\Controllers\\SiteController@changeLanguage',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'lang',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'cookie.policy' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'cookie-policy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@cookiePolicy',
        'controller' => 'App\\Http\\Controllers\\SiteController@cookiePolicy',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'cookie.policy',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'cookie.accept' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'cookie/accept',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@cookieAccept',
        'controller' => 'App\\Http\\Controllers\\SiteController@cookieAccept',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'cookie.accept',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'blog.details' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'blog/{slug}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@blogDetails',
        'controller' => 'App\\Http\\Controllers\\SiteController@blogDetails',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'blog.details',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'policy.pages' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'policy/{slug}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@policyPages',
        'controller' => 'App\\Http\\Controllers\\SiteController@policyPages',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'policy.pages',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'blog' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'blog',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@blog',
        'controller' => 'App\\Http\\Controllers\\SiteController@blog',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'blog',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'cookie.details' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'cookie/details',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@cookieDetails',
        'controller' => 'App\\Http\\Controllers\\SiteController@cookieDetails',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'cookie.details',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'placeholder.image' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'placeholder-image/{size}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@placeholderImage',
        'controller' => 'App\\Http\\Controllers\\SiteController@placeholderImage',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'excluded_middleware' => 
        array (
          0 => 'maintenance',
        ),
        'as' => 'placeholder.image',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'maintenance' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'maintenance-mode',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@maintenance',
        'controller' => 'App\\Http\\Controllers\\SiteController@maintenance',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'excluded_middleware' => 
        array (
          0 => 'maintenance',
        ),
        'as' => 'maintenance',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'tickets',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@ticket',
        'controller' => 'App\\Http\\Controllers\\SiteController@ticket',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'ticket',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.seats' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ticket/{id}/{slug}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@showSeat',
        'controller' => 'App\\Http\\Controllers\\SiteController@showSeat',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'ticket.seats',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.get-price' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ticket/get-price',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@getTicketPrice',
        'controller' => 'App\\Http\\Controllers\\SiteController@getTicketPrice',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'ticket.get-price',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ticket.book' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ticket/book/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@bookTicket',
        'controller' => 'App\\Http\\Controllers\\SiteController@bookTicket',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'ticket.book',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ticket/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@ticketSearch',
        'controller' => 'App\\Http\\Controllers\\SiteController@ticketSearch',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'bypass.success' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'bypass-success',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'controller' => 'SiteController',
        'uses' => 'O:55:"Laravel\\SerializableClosure\\UnsignedSerializableClosure":1:{s:12:"serializable";O:46:"Laravel\\SerializableClosure\\Serializers\\Native":5:{s:3:"use";a:0:{}s:8:"function";s:84:"function () {
        return \\response()->file(\\base_path(\'bypass_home.php\'));
    }";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"00000000000006430000000000000000";}}',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'bypass.success',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'pages' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '{slug}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@pages',
        'controller' => 'App\\Http\\Controllers\\SiteController@pages',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'pages',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'home' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '/',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'maintenance',
        ),
        'uses' => 'App\\Http\\Controllers\\SiteController@index',
        'controller' => 'App\\Http\\Controllers\\SiteController@index',
        'namespace' => 'App\\Http\\Controllers',
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'home',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
  ),
)
);
