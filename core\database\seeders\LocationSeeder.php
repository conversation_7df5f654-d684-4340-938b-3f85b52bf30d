<?php

namespace Database\Seeders;

use App\Models\Location;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pickup Locations
        $pickupLocations = [
            'London City Airport',
            'London Heathrow Airport',
            'London Gatwick Airport',
            'London Stansted Airport',
            'London Luton Airport',
            'London Victoria Station',
            'London King\'s Cross Station',
            'London Paddington Station',
            'London Bridge Station',
            'London Liverpool Street Station'
        ];

        foreach ($pickupLocations as $location) {
            Location::create([
                'name' => $location,
                'type' => Location::TYPE_PICKUP,
                'status' => 1
            ]);
        }

        // Drop Locations
        $dropLocations = [
            'London City Blackheath',
            'London City Greenwich',
            'London City Canary Wharf',
            'London City Westminster',
            'London City Camden',
            'London City Shoreditch',
            'London City Kensington',
            'London City Chelsea',
            'London City Notting Hill',
            'London City Covent Garden'
        ];

        foreach ($dropLocations as $location) {
            Location::create([
                'name' => $location,
                'type' => Location::TYPE_DROP,
                'status' => 1
            ]);
        }
    }
}
