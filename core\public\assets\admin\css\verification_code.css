@import url('https://fonts.googleapis.com/css2?family=Courier+Prime&display=swap');

.login-area{
    width: 480px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background-color: #1E157D;
    padding: 40px;
}

.verification-code {
    display: flex;
    position: relative;
    z-index: 1;
    height: 50px;
    width: 100%;
}


.verification-code::after {
    position: absolute;
    content: '';
    right: -37px;
    width: 35px;
    height: 50px;
    background-color: #1E157D;
    z-index: 2;
}

.verification-code input {
    position: absolute;
    height: 50px;
    width: calc(100% + 80px);
    left: 0;
    background: transparent;
    border: none;
    font-size: 25px !important;
    font-weight: 800;
    letter-spacing: 51px;
    text-indent: 1px;
    border: none;
    z-index: 1;
    padding-left: 25px;
    font-family: 'Courier Prime', monospace;
    color: #fff !important;
    border-color: transparent !important;
}

.verification-code input:focus {
    outline: none;
    cursor: pointer;
    box-shadow: none;
    border-color: transparent !important;
}
.boxes {
    position: absolute;
    top: 0;
    height: 100%;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    z-index: -1;
}
.verification-code span {
    height: 50px;
    width: calc((100% / 6) - 3px);
    background: #F1F1F1;
    border: solid 1px #F1F1F1;
    text-align: center;
    line-height: 50px;
    color: #CDC8C8
}

.verification-code span {
    height: 50px;
    width: calc((100% / 6) - 3px);
    background: #f1f1f100;
    border: solid 1px #ffffff75;
    text-align: center;
    line-height: 50px;
    color: #cdc8c8;
}

.login-area::after {
    z-index: -1;
}


.verification-text{
    font-size: 1.2rem;
}


@media (max-width: 575px) {
    .login-area {
        width: 475px;
        padding: 32px;
    }

    .verification-text{
        font-size: 1rem;
    }

    .verification-code input {
        width: calc(100% + 72px);
        padding-left: 27px;
        letter-spacing: 53px;
    }
   
    .verification-code::after {
        right: -32px;
        width: 30px;
    }
}


@media (max-width: 500px) {
    .login-area {
        width: 400px;
        padding: 28px;
    }

    .verification-text{
        font-size: 0.9rem;
    }

    .verification-code input {
        padding-left: 20px;
        letter-spacing: 42px;
    }

    .verification-code::after {
        right: -28px;
        width: 26px;
    }
}

@media (max-width: 430px) {
    .login-area {
        width: 380px;
    }

    .verification-code input {
        letter-spacing: 38px;
    }
}


@media (max-width: 406px) {
    .login-area {
        width: 340px;
    }

    .verification-code input {
        height: 40px;
        padding-left: 15px;
        letter-spacing: 35px;
        font-size: 20px !important;
    }

    .verification-code {
        height: 40px;
    }
    
    .verification-code::after {
        height: 40px;
    }

    .verification-code span{
        height: 40px;
        line-height: 40px;
    }
}

@media (max-width: 366px) {
    .login-area {
        width: 300px;
    }
    .verification-code input {
        padding-left: 14px;
        letter-spacing: 28px;
    }
}

@media (max-width: 328px) {
    .login-area {
        width: 290px;
    }
    .verification-code input {
        padding-left: 13px;
        letter-spacing: 27px;
    }
}