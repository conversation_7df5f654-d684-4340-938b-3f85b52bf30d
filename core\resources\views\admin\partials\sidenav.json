{"dashboard": {"keyword": ["Dashboard", "Home", "Panel", "Admin", "Control center", "Overview", "Main hub", "Management hub", "Administrative hub", "Central hub", "Command center", "Administrator portal", "Centralized interface", "Admin console", "Management dashboard", "Main screen", "Administrative dashboard", "Command dashboard", "Main control panel"], "title": "Dashboard", "icon": "las la-home", "route_name": "admin.dashboard", "menu_active": "admin.dashboard"}, "manage_users": {"title": "Manage Users", "icon": "las la-users", "counters": ["bannedUsersCount", "emailUnverifiedUsersCount", "mobileUnverifiedUsersCount"], "menu_active": "admin.users*", "submenu": [{"keyword": ["active users", "Manage Users", "User management", "User control", "User status", "User activity", "User analytics"], "title": "Active Users", "route_name": "admin.users.active", "menu_active": "admin.users.active"}, {"keyword": ["banned users", "Manage Users", "User management", "Account bans", "User activity"], "title": "Banned Users", "route_name": "admin.users.banned", "menu_active": "admin.users.banned", "counter": "bannedUsersCount"}, {"keyword": ["email unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Email Unverified", "route_name": "admin.users.email.unverified", "menu_active": "admin.users.email.unverified", "counter": "emailUnverifiedUsersCount"}, {"keyword": ["mobile unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Mobile Unverified", "route_name": "admin.users.mobile.unverified", "menu_active": "admin.users.mobile.unverified", "counter": "mobileUnverifiedUsersCount"}, {"keyword": ["all users users", "Manage Users", "User management", "User control", "User activity", "User analytics"], "title": "All Users", "route_name": "admin.users.all", "menu_active": "admin.users.all"}, {"keyword": ["send notification users", "Manage Users", "User notifications", "User management", "User activity"], "title": "Send Notification", "route_name": "admin.users.notification.all", "menu_active": "admin.users.notification.all"}]}, "support_ticket": {"title": "Support Ticket", "icon": "la la-ticket", "counters": ["pendingTicketCount"], "menu_active": "admin.ticket*", "submenu": [{"keyword": ["Pending Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket status", "Ticket activity"], "title": "Pending Ticket", "route_name": "admin.ticket.pending", "menu_active": "admin.ticket.pending", "counter": "pendingTicketCount"}, {"keyword": ["Closed Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Closed Ticket", "route_name": "admin.ticket.closed", "menu_active": "admin.ticket.closed"}, {"keyword": ["Answered Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Answered Ticket", "route_name": "admin.ticket.answered", "menu_active": "admin.ticket.answered"}, {"keyword": ["All Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket activity"], "title": "All Ticket", "route_name": "admin.ticket.index", "menu_active": "admin.ticket.index"}]}, "reports": {"title": "Report", "icon": "la la-list", "menu_active": "admin.report*", "submenu": [{"keyword": ["Transaction Log", "Report", "Transaction report", "Transaction history", "Transaction activity", "balance sheet", "balance log", "balance history"], "title": "Transaction History", "route_name": "admin.report.transaction", "menu_active": ["admin.report.transaction", "admin.report.transaction.search"], "params": {"user_id": ""}}, {"keyword": ["Login History", "Report", "Login report", "Login history", "Login activity"], "title": "Login History", "route_name": "admin.report.login.history", "menu_active": ["admin.report.login.history", "admin.report.login.ipHistory"]}, {"keyword": ["Notification History", "Report", "Notification report", "Notification history", "Notification activity", "email log", "email history", "sms log", "sms history", "push notification log", "push notification history"], "title": "Notification History", "route_name": "admin.report.notification.history", "menu_active": "admin.report.notification.history"}]}, "bus_management": {"keyword": ["Bus Management", "Location Management", "Pickup Location", "Drop Location", "Route Management", "Bus Routes", "Location Settings", "Transport Management"], "header": "Bus Management", "title": "Bus Management", "icon": "las la-bus", "menu_active": "admin.bus*", "submenu": [{"keyword": ["Pickup Locations", "Bus Management", "Location Management", "Pickup Points", "Starting Points", "Origin Locations"], "title": "Pickup Locations", "route_name": "admin.bus.pickup.locations", "menu_active": "admin.bus.pickup.locations*"}, {"keyword": ["Drop Locations", "Bus Management", "Location Management", "Drop Points", "Destination Points", "Drop Off Locations"], "title": "Drop Locations", "route_name": "admin.bus.drop.locations", "menu_active": "admin.bus.drop.locations*"}]}, "system_setting": {"keyword": ["System Setting", "setting", "System configuration", "System preferences", "Configuration management", "System setup"], "header": "Settings", "title": "System Setting", "icon": "las la-life-ring", "route_name": "admin.setting.system", "menu_active": ["admin.setting.system", "admin.setting.general", "admin.setting.socialite.credentials", "admin.setting.system.configuration", "admin.setting.logo.icon", "admin.extensions.index", "admin.language.manage", "admin.language.key", "admin.seo", "admin.frontend.templates", "admin.frontend.manage.*", "admin.maintenance.mode", "admin.setting.cookie", "admin.setting.custom.css", "admin.setting.sitemap", "admin.setting.robot", "admin.setting.search.form", "admin.setting.notification.global.email", "admin.setting.notification.global.sms", "admin.setting.notification.global.push", "admin.setting.notification.email", "admin.setting.notification.sms", "admin.setting.notification.push", "admin.setting.notification.templates", "admin.setting.notification.template.edit", "admin.frontend.index", "admin.frontend.sections*", "admin.gateway*", "admin.setting.app.purchase"]}, "extra": {"header": "Extra", "title": "Extra", "icon": "la la-server", "menu_active": "admin.system*", "counters": ["updateAvailable"], "submenu": [{"keyword": ["Application", "System", "Application management", "Application settings", "System information", "version", "laravel", "timezone"], "title": "Application", "route_name": "admin.system.info", "menu_active": "admin.system.info"}, {"keyword": ["Server", "System", "Server management", "Server settings", "System information", "version", "php version", "software", "ip address", "server ip address", "server port", "http host"], "title": "Server", "route_name": "admin.system.server.info", "menu_active": "admin.system.server.info"}, {"keyword": ["<PERSON><PERSON>", "System", "Cache management", "Cache optimization", "System performance", "clear cache"], "title": "<PERSON><PERSON>", "route_name": "admin.system.optimize", "menu_active": "admin.system.optimize"}, {"keyword": ["Update", "System", "Update management", "System update", "Software updates", "version update", "upgrade", "latest version"], "title": "Update", "route_name": "admin.system.update", "menu_active": "admin.system.update*", "counter": "updateAvailable"}]}}