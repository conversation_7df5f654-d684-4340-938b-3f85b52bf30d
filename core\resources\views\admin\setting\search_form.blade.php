@extends('admin.layouts.app')
@section('panel')
    @php
        $searchSettings = getContent('search_form.settings', true);
        $settings = $searchSettings ? $searchSettings->data_values : null;
    @endphp
    
    <div class="row mb-none-30">
        <div class="col-lg-12 col-md-12 mb-30">
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="alert alert-info">
                                    <i class="las la-info-circle"></i>
                                    <strong>@lang('Search Form Redirection Settings')</strong><br>
                                    @lang('Configure how the search form redirects to external booking systems. Set the target domain and parameter names that will be used in the URL.')
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required">@lang('Redirect Domain')</label>
                                    <input class="form-control" type="url" name="search_redirect_domain" 
                                           required value="{{ @$settings->redirect_domain }}" 
                                           placeholder="https://example.com">
                                    <small class="text-muted">@lang('The domain where search results will be redirected (e.g., https://booking.example.com)')</small>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('Currency Parameter')</label>
                                    <input class="form-control" type="text" name="search_currency_param"
                                           value="{{ @$settings->currency_param ?? 'ddCurrency' }}"
                                           placeholder="ddCurrency">
                                    <small class="text-muted">@lang('URL parameter name for currency (default: $ for Dollar)')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required">@lang('From Location Parameter')</label>
                                    <input class="form-control" type="text" name="search_from_param"
                                           required value="{{ @$settings->from_param ?? 'ddFrom' }}"
                                           placeholder="ddFrom">
                                    <small class="text-muted">@lang('URL parameter name for departure location')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required">@lang('To Location Parameter')</label>
                                    <input class="form-control" type="text" name="search_to_param"
                                           required value="{{ @$settings->to_param ?? 'ddTo' }}"
                                           placeholder="ddTo">
                                    <small class="text-muted">@lang('URL parameter name for destination location')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required">@lang('Departure Date Parameter')</label>
                                    <input class="form-control" type="text" name="search_departure_param"
                                           required value="{{ @$settings->departure_param ?? 'deptdate' }}"
                                           placeholder="deptdate">
                                    <small class="text-muted">@lang('URL parameter name for departure date (YYYY-MM-DD format)')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('Return Date Parameter')</label>
                                    <input class="form-control" type="text" name="search_return_param"
                                           value="{{ @$settings->return_param ?? 'rtndate' }}"
                                           placeholder="rtndate">
                                    <small class="text-muted">@lang('URL parameter name for return date (optional for round trips)')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('Passengers Parameter')</label>
                                    <input class="form-control" type="text" name="search_passengers_param"
                                           value="{{ @$settings->passengers_param ?? 'pax' }}"
                                           placeholder="pax">
                                    <small class="text-muted">@lang('URL parameter name for number of passengers (default: 1)')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('Trip Type Parameter')</label>
                                    <input class="form-control" type="text" name="search_triptype_param"
                                           value="{{ @$settings->triptype_param ?? 'way' }}"
                                           placeholder="way">
                                    <small class="text-muted">@lang('URL parameter name for trip type (1=One Way, 2=Round Trip)')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('Transportation Type Parameter')</label>
                                    <input class="form-control" type="text" name="search_transport_param"
                                           value="{{ @$settings->transport_param ?? 'type' }}"
                                           placeholder="type">
                                    <small class="text-muted">@lang('URL parameter name for transportation type (e.g., Bus)')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('State/Region Parameter')</label>
                                    <input class="form-control" type="text" name="search_state_param"
                                           value="{{ @$settings->state_param ?? 'sbf' }}"
                                           placeholder="sbf">
                                    <small class="text-muted">@lang('URL parameter name for state/region of departure city')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('From Filter Parameter')</label>
                                    <input class="form-control" type="text" name="search_from_filter_param"
                                           value="{{ @$settings->from_filter_param ?? 'ddFromFilter' }}"
                                           placeholder="ddFromFilter">
                                    <small class="text-muted">@lang('URL parameter name for departure location filter (usually set to undefined)')</small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>@lang('To Filter Parameter')</label>
                                    <input class="form-control" type="text" name="search_to_filter_param"
                                           value="{{ @$settings->to_filter_param ?? 'ddtofilter' }}"
                                           placeholder="ddtofilter">
                                    <small class="text-muted">@lang('URL parameter name for destination location filter (usually set to undefined)')</small>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0">@lang('Example URL Structure')</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2"><strong>@lang('Generated URL will look like:')</strong></p>
                                        <code id="example-url">
                                            {{ @$settings->redirect_domain ?? 'https://bus.superiortour.com.sg/booking-new/' }}?{{ @$settings->currency_param ?? 'ddCurrency' }}=$&{{ @$settings->from_param ?? 'ddFrom' }}=Singapore&{{ @$settings->to_param ?? 'ddTo' }}=Legoland&{{ @$settings->departure_param ?? 'deptdate' }}=2025-09-30&{{ @$settings->return_param ?? 'rtndate' }}=2025-10-02&{{ @$settings->passengers_param ?? 'pax' }}=1&{{ @$settings->triptype_param ?? 'way' }}=2&{{ @$settings->from_filter_param ?? 'ddFromFilter' }}=undefined&{{ @$settings->to_filter_param ?? 'ddtofilter' }}=undefined&{{ @$settings->transport_param ?? 'type' }}=Bus&{{ @$settings->state_param ?? 'sbf' }}=Singapore
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn--primary w-100 h-45">@lang('Submit')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
    'use strict';
    
    // Update example URL when inputs change
    function updateExampleUrl() {
        const domain = $('input[name="search_redirect_domain"]').val() || 'https://bus.superiortour.com.sg/booking-new/';
        const currencyParam = $('input[name="search_currency_param"]').val() || 'ddCurrency';
        const fromParam = $('input[name="search_from_param"]').val() || 'ddFrom';
        const toParam = $('input[name="search_to_param"]').val() || 'ddTo';
        const departureParam = $('input[name="search_departure_param"]').val() || 'deptdate';
        const returnParam = $('input[name="search_return_param"]').val() || 'rtndate';
        const passengersParam = $('input[name="search_passengers_param"]').val() || 'pax';
        const triptypeParam = $('input[name="search_triptype_param"]').val() || 'way';
        const fromFilterParam = $('input[name="search_from_filter_param"]').val() || 'ddFromFilter';
        const toFilterParam = $('input[name="search_to_filter_param"]').val() || 'ddtofilter';
        const transportParam = $('input[name="search_transport_param"]').val() || 'type';
        const stateParam = $('input[name="search_state_param"]').val() || 'sbf';

        const exampleUrl = `${domain}?${currencyParam}=$&${fromParam}=Singapore&${toParam}=Legoland&${departureParam}=2025-09-30&${returnParam}=2025-10-02&${passengersParam}=1&${triptypeParam}=2&${fromFilterParam}=undefined&${toFilterParam}=undefined&${transportParam}=Bus&${stateParam}=Singapore`;

        $('#example-url').text(exampleUrl);
    }
    
    // Update example URL on input change
    $('input[name^="search_"]').on('input', updateExampleUrl);
    
    // Initial update
    updateExampleUrl();
</script>
@endpush
