@extends($activeTemplate.'layouts.frontend')
@section('content')
   @include($activeTemplate.'partials.banner')
    @if($sections->secs != null)
        @php
            $decodedSections = is_string($sections->secs) ? json_decode($sections->secs) : $sections->secs;
            $decodedSections = is_array($decodedSections) || is_object($decodedSections) ? $decodedSections : [];
        @endphp
        @foreach($decodedSections as $sec)
            @include($activeTemplate.'sections.'.$sec)
        @endforeach
    @endif
@endsection
