@extends($activeTemplate.'layouts.frontend')
@section('content')

    @if($sections != null)
        @php
            $decodedSections = is_string($sections) ? json_decode($sections) : $sections;
            $decodedSections = is_array($decodedSections) || is_object($decodedSections) ? $decodedSections : [];
        @endphp
        @foreach($decodedSections as $sec)
            @include($activeTemplate.'sections.'.$sec)
        @endforeach
    @endif
@endsection
