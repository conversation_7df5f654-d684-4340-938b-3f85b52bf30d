@php
    $contents = getContent('banner.content', true);
    $searchSettings = getContent('search_form.settings', true);
    $settings = $searchSettings ? (is_string($searchSettings->data_values) ? json_decode($searchSettings->data_values) : $searchSettings->data_values) : null;

    try {
        $pickupLocations = App\Models\Location::pickup()->active()->get();
        $dropLocations = App\Models\Location::drop()->active()->get();
    } catch (Exception $e) {
        $pickupLocations = collect(); // Empty collection if query fails
        $dropLocations = collect(); // Empty collection if query fails
    }
@endphp

<!-- Banner Section Starts Here -->
<section class="banner-section"
    style="background: url({{ getImage('assets/images/frontend/banner/' . @$contents->data_values->background_image, '1500x88') }}) repeat-x bottom;">
    <div class="container">
        <div class="banner-wrapper">
            <div class="banner-content">
                @if($contents && $contents->data_values && $contents->data_values->heading)
                    <h1 class="title">{{ __($contents->data_values->heading) }}</h1>
                @else
                    <h1 class="title">DEBUG: Banner content not found</h1>
                    <!-- Debug info: {{ json_encode($contents) }} -->
                @endif
            </div>
            <div class="ticket-form-wrapper">
                <div class="ticket-header nav-tabs nav border-0">
                    <h4 class="title">@lang('Choose Your Ticket')</h4>
                </div>
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="one-way">
                        <!-- Hidden input for trip type - always round trip -->
                        <input type="hidden" name="trip_type" value="round_trip">

                        <div class="ultra-wide-search-form">
                            <form id="bookingForm" class="search-form-container">
                                <!-- Pickup Location -->
                                <div class="form-field pickup-field">
                                    <div class="field-icon">
                                        <i class="las la-map-marker-alt"></i>
                                    </div>
                                    <div class="field-info">
                                        <label class="field-title">@lang('Pickup Location')</label>
                                        <select name="pickup" id="pickupLocation" class="field-value select2" data-placeholder="@lang('Select Pickup Location')">
                                            <option value="">@lang('Select Pickup Location')</option>
                                            @foreach ($pickupLocations as $location)
                                                <option value="{{ $location->id }}" data-slug="{{ $location->slug }}"
                                                    @if (request()->pickup == $location->id) selected @endif>
                                                    {{ __($location->name) }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <!-- Separator -->
                                <div class="field-separator"></div>

                                <!-- Drop Location -->
                                <div class="form-field drop-field">
                                    <div class="field-icon">
                                        <i class="las la-map-marker-alt"></i>
                                    </div>
                                    <div class="field-info">
                                        <label class="field-title">@lang('Drop Location')</label>
                                        <select name="destination" id="dropLocation" class="field-value select2" data-placeholder="@lang('Select Drop Location')">
                                            <option value="">@lang('Select Drop Location')</option>
                                            @foreach ($dropLocations as $location)
                                                <option value="{{ $location->id }}" data-slug="{{ $location->slug }}"
                                                    @if (request()->destination == $location->id) selected @endif>
                                                    {{ __($location->name) }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <!-- Separator -->
                                <div class="field-separator"></div>

                                <!-- Date -->
                                <div class="form-field date-field">
                                    <div class="field-icon">
                                        <i class="las la-calendar"></i>
                                    </div>
                                    <div class="field-info">
                                        <label class="field-title">@lang('Depart Date')</label>
                                        <input type="text" name="date_of_journey" id="departDate" class="field-value date-range" placeholder="Select Departure Date" autocomplete="off" style="text-align: center;">
                                    </div>
                                </div>

                                <!-- Separator -->
                                <div class="field-separator return-separator"></div>

                                <!-- Return Date (Always visible for round trip) -->
                                <div class="form-field return-date-field">
                                    <div class="field-icon">
                                        <i class="las la-calendar"></i>
                                    </div>
                                    <div class="field-info">
                                        <label class="field-title">@lang('Return Date')</label>
                                        <input type="text" name="return_date" id="returnDate" class="field-value date-range-return" placeholder="Select Return Date" autocomplete="off" style="text-align: center;">
                                    </div>
                                </div>



                                <!-- Submit Button -->
                                <button type="submit" class="submit-btn">
                                    @lang('Submit')
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="shape">
        <img src="{{ getImage('assets/images/frontend/banner/' . @$contents->data_values->animation_image, '200x69') }}"
            alt="bg">
    </div>
</section>


@push('style-lib')
    <link rel="stylesheet" href="{{ asset('assets/global/css/select2.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/css/daterangepicker.css') }}">
@endpush

@push('style')
    <style>
        .trip-type-selection {
            margin-bottom: 20px;
        }

        .trip-type-selection .form-check-input {
            background-color: #fff;
            border: 2px solid #333;
            margin-right: 8px;
            width: 18px;
            height: 18px;
        }

        .trip-type-selection .form-check-input:checked {
            background-color: #f39c12;
            border-color: #f39c12;
        }

        .trip-type-selection .form-check-input:focus {
            border-color: #f39c12;
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
        }

        .trip-type-selection .form-check-input:hover {
            border-color: #f39c12;
        }

        .trip-type-selection .form-check-input:focus {
            border-color: #f39c12;
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
        }

        .trip-type-selection .form-check-label {
            color: #000 !important;
            font-weight: 500;
            margin-right: 25px;
        }

        /* Date input styling */
        .date-range, .date-range-return {
            text-align: center !important;
        }

        .field-value.date-range, .field-value.date-range-return {
            text-align: center !important;
            font-weight: 500;
        }

        /* Banner Layout Adjustments */
        .banner-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-height: 500px;
            justify-content: center;
        }

        .banner-content {
            margin-bottom: 50px;
        }

        .banner-content .title {
            font-size: 3rem;
            margin-bottom: 0;
        }

        .ticket-form-wrapper {
            width: 100%;
            max-width: 1400px;
        }

        .ticket-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .ticket-header .title {
            color: #000;
            font-size: 1.5rem;
            margin-bottom: 0;
        }

        .trip-type-selection {
            text-align: center;
            margin-bottom: 30px;
        }

        /* Ultra Wide Search Form Styles */
        .ultra-wide-search-form {
            background: #fff;
            border-radius: 50px;
            padding: 15px 25px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin: 0 auto;
            max-width: 1400px;
            width: 95%;
        }

        .search-form-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            min-height: 60px;
            gap: 0;
        }

        .form-field {
            display: flex;
            align-items: center;
            flex: 1;
            padding: 0 20px;
            min-width: 200px;
        }

        .field-icon {
            margin-right: 15px;
            font-size: 20px;
            color: #666;
            width: 20px;
            text-align: center;
        }

        .field-info {
            flex: 1;
        }

        .field-title {
            font-size: 12px;
            color: #666;
            font-weight: 600;
            margin: 0 0 2px 0;
            display: block;
        }

        .field-value {
            border: none;
            background: transparent;
            font-size: 15px;
            font-weight: 600;
            color: #333;
            width: 100%;
            outline: none;
            padding: 0;
            line-height: 1.2;
        }

        .field-value::placeholder {
            color: #333;
            font-weight: 600;
        }

        .field-separator {
            width: 1px;
            height: 40px;
            background: #e0e0e0;
            margin: 0 10px;
        }



        .submit-btn {
            background: #2c3e50;
            color: #fff;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-left: 20px;
            white-space: nowrap;
        }

        .submit-btn:hover {
            background: #34495e;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
        }

        .return-date-field {
            transition: all 0.3s ease;
        }

        .return-separator {
            transition: all 0.3s ease;
        }

        /* Select2 Customization */
        .select2-container {
            width: 100% !important;
        }

        .select2-container .select2-selection--single {
            border: none !important;
            background: transparent !important;
            height: auto !important;
            padding: 0 !important;
        }

        .select2-container .select2-selection--single .select2-selection__rendered {
            color: #333 !important;
            font-weight: 600 !important;
            font-size: 15px !important;
            padding: 0 !important;
            line-height: 1.2 !important;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1399px) {
            .ultra-wide-search-form {
                max-width: 1200px;
            }

            .form-field {
                min-width: 180px;
                padding: 0 15px;
            }

            .field-title {
                font-size: 11px;
            }

            .field-value {
                font-size: 14px;
            }

            .banner-content .title {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 1199px) {
            .banner-wrapper {
                min-height: 400px;
            }

            .banner-content {
                margin-bottom: 40px;
            }

            .banner-content .title {
                font-size: 2rem;
            }

            .search-form-container {
                flex-wrap: wrap;
                gap: 15px;
                justify-content: center;
            }

            .form-field {
                min-width: 250px;
                background: #f8f9fa;
                border-radius: 15px;
                padding: 15px 20px;
                margin: 5px;
            }

            .field-separator {
                display: none;
            }



            .submit-btn {
                margin: 10px;
                width: 200px;
            }
        }

        @media (max-width: 767px) {
            .banner-wrapper {
                min-height: 350px;
                padding: 20px 0;
            }

            .banner-content {
                margin-bottom: 30px;
            }

            .banner-content .title {
                font-size: 1.8rem;
            }

            .ultra-wide-search-form {
                border-radius: 20px;
                padding: 20px 15px;
                width: 98%;
            }

            .form-field {
                min-width: 100%;
                margin: 5px 0;
            }

            .submit-btn {
                width: 100%;
                margin: 15px 0 0 0;
            }

            .ticket-header .title {
                font-size: 1.2rem;
            }
        }
    </style>
@endpush

@push('script-lib')
    <script src="{{ asset('assets/global/js/select2.min.js') }}"></script>
    <script src="{{ asset('assets/global/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/global/js/daterangepicker.min.js') }}"></script>
@endpush


@push('script')
    <script>
        (function($) {
            "use strict"

            $('.select2').select2();

            // Departure date picker
            const datePicker = $('.date-range').daterangepicker({
                autoUpdateInput: true,
                singleDatePicker: true,
                minDate: new Date(),
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            // Return date picker
            let returnDatePicker = $('.date-range-return').daterangepicker({
                autoUpdateInput: true,
                singleDatePicker: true,
                minDate: new Date(),
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            // Don't initialize return date - leave it empty until user selects departure date

            // Function to update return date picker
            function updateReturnDatePicker(minDate) {
                // Destroy and recreate the return date picker with new minDate
                $('.date-range-return').data('daterangepicker').remove();
                returnDatePicker = $('.date-range-return').daterangepicker({
                    autoUpdateInput: true,
                    singleDatePicker: true,
                    minDate: minDate,
                    locale: {
                        format: 'YYYY-MM-DD'
                    }
                });
                console.log('Return date picker recreated with minDate:', minDate.format('YYYY-MM-DD'));
            }

            // Update return date when departure date changes
            $('.date-range').on('apply.daterangepicker', function(ev, picker) {
                const selectedDate = picker.startDate;
                console.log('Depart date selected:', selectedDate.format('YYYY-MM-DD'));

                // Always set return date to match departure date when departure date is selected
                $('.date-range-return').val(selectedDate.format('YYYY-MM-DD'));
                console.log('Set return date to match departure date:', selectedDate.format('YYYY-MM-DD'));

                // Update return date picker with new minimum date
                updateReturnDatePicker(selectedDate);
            });

            // Trip type toggle
            $('input[name="trip_type"]').on('change', function() {
                if ($(this).val() === 'round_trip') {
                    $('.return-date-field').show();
                    $('.return-separator').show();
                } else {
                    $('.return-date-field').hide();
                    $('.return-separator').hide();
                }
            });

            // Form submission handler
            $('#bookingForm').on('submit', function(e) {
                e.preventDefault();

                // Get form values with better Select2 handling
                const pickupId = $('#pickupLocation').val() || $('select[name="pickup"]').val();
                const dropId = $('#dropLocation').val() || $('select[name="destination"]').val();
                const departDate = $('#departDate').val() || $('input[name="date_of_journey"]').val();
                const returnDate = $('#returnDate').val() || $('input[name="return_date"]').val();
                const tripType = $('input[name="trip_type"]').val() || 'round_trip'; // Always round trip

                // Debug logging
                console.log('Form values:', {
                    pickupId: pickupId,
                    dropId: dropId,
                    departDate: departDate,
                    returnDate: returnDate,
                    tripType: tripType
                });

                // Validation
                if (!pickupId || pickupId === '') {
                    alert('Please select a pickup location');
                    console.log('Pickup validation failed:', pickupId);
                    return;
                }
                if (!dropId || dropId === '') {
                    alert('Please select a drop location');
                    console.log('Drop validation failed:', dropId);
                    return;
                }
                if (!departDate || departDate === '') {
                    alert('Please select a departure date');
                    console.log('Date validation failed:', departDate);
                    return;
                }
                // Validate return date is provided (should be auto-filled when departure date is selected)
                if (!returnDate || returnDate === '') {
                    alert('Please select a departure date first. Return date will be automatically set.');
                    console.log('Return date validation failed:', returnDate);
                    return;
                }

                // Validate return date is not before depart date
                if (returnDate && departDate) {
                    const departMoment = moment(departDate);
                    const returnMoment = moment(returnDate);
                    if (returnMoment.isBefore(departMoment)) {
                        alert('Return date cannot be before departure date');
                        console.log('Return date before depart date:', {departDate, returnDate});
                        return;
                    }
                }

                // Get location slugs with fallback
                const pickupSlug = $('#pickupLocation option:selected').data('slug') ||
                                 $('select[name="pickup"] option:selected').data('slug');
                const dropSlug = $('#dropLocation option:selected').data('slug') ||
                               $('select[name="destination"] option:selected').data('slug');

                console.log('Location slugs:', {
                    pickupSlug: pickupSlug,
                    dropSlug: dropSlug
                });

                // Get search form settings from database
                console.log('Search settings from PHP:', @json($settings));
                const searchSettings = @json($settings);
                console.log('Parsed search settings:', searchSettings);

                // Use default values if settings not configured
                const redirectDomain = searchSettings?.redirect_domain || 'https://bus.superiortour.com.sg/booking-new/';
                console.log('Redirect domain:', redirectDomain);
                const currencyParam = searchSettings?.currency_param || 'ddCurrency';
                const fromParam = searchSettings?.from_param || 'ddFrom';
                const toParam = searchSettings?.to_param || 'ddTo';
                const departureParam = searchSettings?.departure_param || 'deptdate';
                const returnParam = searchSettings?.return_param || 'rtndate';
                const passengersParam = searchSettings?.passengers_param || 'pax';
                const triptypeParam = searchSettings?.triptype_param || 'way';
                const transportParam = searchSettings?.transport_param || 'type';
                const stateParam = searchSettings?.state_param || 'sbf';
                const fromFilterParam = searchSettings?.from_filter_param || 'ddFromFilter';
                const toFilterParam = searchSettings?.to_filter_param || 'ddtofilter';

                // Get location names for URL parameters (trim whitespace)
                const pickupName = ($('#pickupLocation option:selected').text() ||
                                 $('select[name="pickup"] option:selected').text()).trim();
                const dropName = ($('#dropLocation option:selected').text() ||
                               $('select[name="destination"] option:selected').text()).trim();

                // Debug: Log location names
                console.log('Pickup name:', `"${pickupName}"`);
                console.log('Drop name:', `"${dropName}"`);

                // Validation - ensure we have valid location names
                if (!pickupName || pickupName === '') {
                    alert('Please select a valid pickup location');
                    return;
                }
                if (!dropName || dropName === '') {
                    alert('Please select a valid destination location');
                    return;
                }

                // Format dates (YYYY-MM-DD)
                const formattedDepartDate = moment(departDate).format('YYYY-MM-DD');

                // Build URL parameters
                const urlParams = new URLSearchParams();

                // Add currency parameter (default to $ for Dollar)
                urlParams.append(currencyParam, '$');

                // Add location parameters
                urlParams.append(fromParam, pickupName);
                urlParams.append(toParam, dropName);

                // Add date parameters
                urlParams.append(departureParam, formattedDepartDate);

                // Add return date - always include for Superior Tour system
                // For round trip, use the return date (which defaults to departure date if not set)
                const formattedReturnDate = moment(returnDate).format('YYYY-MM-DD');
                urlParams.append(returnParam, formattedReturnDate);

                // Add passengers parameter (default to 1)
                urlParams.append(passengersParam, '1');

                // Add trip type parameter - always 2 for round trip
                urlParams.append(triptypeParam, '2');

                // Add filter parameters (required by Superior Tour system)
                urlParams.append(fromFilterParam, 'undefined');
                urlParams.append(toFilterParam, 'undefined');

                // Add transportation type parameter (default to Bus)
                urlParams.append(transportParam, 'Bus');

                // Add state/region parameter - always use Singapore as default
                const pickupState = $('#pickupLocation option:selected').data('state') ||
                                  $('select[name="pickup"] option:selected').data('state') ||
                                  'Singapore'; // Default to Singapore for all trips
                urlParams.append(stateParam, pickupState);

                // Build final URL using parameter-based system
                let bookingUrl = `${redirectDomain}?${urlParams.toString()}`;

                // Debug: Log the URL
                console.log('Generated booking URL:', bookingUrl);
                console.log('Search settings used:', searchSettings);

                // Redirect to booking system
                window.open(bookingUrl, '_blank');
            });



            // Initialize Select2 with custom styling
            $('.select2').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('body'),
                placeholder: function() {
                    return $(this).data('placeholder') || this.getAttribute('placeholder') || 'Select an option';
                },
                allowClear: false,
                templateResult: function(data) {
                    return data.text;
                },
                templateSelection: function(data) {
                    return data.text;
                }
            });

            // Debug: Log Select2 initialization
            console.log('Select2 initialized for elements:', $('.select2').length);

            // Initialize with placeholder text
            setTimeout(function() {
                // Ensure placeholders are shown
                if ($('select[name="pickup"]').val() === '') {
                    $('select[name="pickup"]').trigger('change');
                }
                if ($('select[name="destination"]').val() === '') {
                    $('select[name="destination"]').trigger('change');
                }

                // Debug: Add click handler to test values
                window.testFormValues = function() {
                    console.log('=== Form Values Test ===');
                    console.log('Pickup ID:', $('#pickupLocation').val());
                    console.log('Drop ID:', $('#dropLocation').val());
                    console.log('Pickup Name:', $('#pickupLocation option:selected').text());
                    console.log('Drop Name:', $('#dropLocation option:selected').text());
                    console.log('Pickup Slug:', $('#pickupLocation option:selected').data('slug'));
                    console.log('Drop Slug:', $('#dropLocation option:selected').data('slug'));
                };
                console.log('Test function available: testFormValues()');
            }, 100);

        })(jQuery)
    </script>
@endpush
