@extends($activeTemplate . $layout)
@section('content')
    @php
        try {
            $pickupLocations = App\Models\Location::pickup()->active()->get();
            $dropLocations = App\Models\Location::drop()->active()->get();
        } catch (Exception $e) {
            $pickupLocations = collect(); // Empty collection if query fails
            $dropLocations = collect(); // Empty collection if query fails
        }
    @endphp

    <div class="ticket-search-bar bg_img padding-top" style="background: url({{ getImage('assets/templates/basic/images/search_bg.jpg') }}) left center;">
        <div class="container">
            <div class="bus-search-header">
                <form id="ticketBookingForm" class="ticket-form ticket-form-two row g-3 justify-content-center">
                    <div class="col-md-4 col-lg-3">
                        <div class="form--group">
                            <i class="las la-location-arrow"></i>
                            <select name="pickup" id="ticketPickupLocation" class="form--control select2" data-placeholder="@lang('Pickup Point')">
                                <option value="">@lang('Pickup Point')</option>
                                @foreach ($pickupLocations as $location)
                                    <option value="{{ $location->id }}" data-slug="{{ $location->slug }}"
                                        @if (request()->pickup == $location->id) selected @endif>
                                        {{ __($location->name) }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form--group">
                            <i class="las la-map-marker"></i>
                            <select name="destination" id="ticketDropLocation" class="form--control select2" data-placeholder="@lang('Dropping Point')">
                                <option value="">@lang('Dropping Point')</option>
                                @foreach ($dropLocations as $location)
                                    <option value="{{ $location->id }}" data-slug="{{ $location->slug }}"
                                        @if (request()->destination == $location->id) selected @endif>
                                        {{ __($location->name) }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 col-lg-2">
                        <div class="form--group">
                            <i class="las la-calendar-check"></i>
                            <input type="text" name="date_of_journey" class="form--control date-range" placeholder="@lang('Departure Date')" autocomplete="off" value="{{ request()->date_of_journey }}" style="text-align: center;">
                        </div>
                    </div>
                    <div class="col-md-3 col-lg-2">
                        <div class="form--group">
                            <i class="las la-calendar-check"></i>
                            <input type="text" name="return_date" class="form--control date-range-return" placeholder="@lang('Return Date')" autocomplete="off" value="{{ request()->return_date }}" style="text-align: center;">
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-2">
                        <div class="form--group">
                            <button class="btn btn--base w-100">@lang('Find Tickets')</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Ticket Search Starts -->

    <!-- Ticket Section Starts Here -->
    <section class="ticket-section padding-bottom section-bg">
        <div class="container">
            <div class="row gy-5">
                <div class="col-lg-3">
                    <form action="{{ route('search') }}" id="filterForm">
                        <div class="ticket-filter">
                            <div class="filter-header filter-item">
                                <h4 class="title mb-0">@lang('Filter')</h4>
                                <button type="reset" class="reset-button h-auto">@lang('Reset All')</button>
                            </div>
                            @if ($fleetType)
                                <div class="filter-item">
                                    <h5 class="title">@lang('Vehicle Type')</h5>
                                    <ul class="bus-type">
                                        @foreach ($fleetType as $fleet)
                                            <li class="custom--checkbox">
                                                <input name="fleetType[]" class="search" value="{{ $fleet->id }}" id="{{ $fleet->name }}" type="checkbox" @if (request()->fleetType) @foreach (request()->fleetType as $item)
                                    @if ($item == $fleet->id)
                                    checked @endif @endforeach
                                        @endif >
                                        <label for="{{ $fleet->name }}"><span><i class="las la-bus"></i>{{ __($fleet->name) }}</span></label>
                                        </li>
                            @endforeach
                            </ul>
                        </div>
                        @endif

                        @if ($routes)
                            <div class="filter-item">
                                <h5 class="title">@lang('Routes')</h5>
                                <ul class="bus-type">
                                    @foreach ($routes as $route)
                                        <li class="custom--checkbox">
                                            <input name="routes[]" class="search" value="{{ $route->id }}" id="route.{{ $route->id }}" type="checkbox" @if (request()->routes) @foreach (request()->routes as $item)
                                                @if ($item == $route->id) checked @endif @endforeach
                                    @endif >
                                    <label for="route.{{ $route->id }}"><span><span><i class="las la-road"></i>{{ __($route->name) }} </span></label>
                                    </li>
                        @endforeach
                        </ul>
                </div>
                @endif

                @if ($schedules)
                    <div class="filter-item">
                        <h5 class="title">@lang('Schedules')</h5>
                        <ul class="bus-type">
                            @foreach ($schedules as $schedule)
                                <li class="custom--checkbox">
                                    <input name="schedules[]" class="search" value="{{ $schedule->id }}" id="schedule.{{ $schedule->id }}" type="checkbox" @if (request()->schedules) @foreach (request()->schedules as $item)
                                    @if ($item == $schedule->id)
                                    checked @endif @endforeach
                            @endif>
                            <label for="schedule.{{ $schedule->id }}"><span><span><i class="las la-clock"></i>
                                        {{ showDateTime($schedule->start_from, 'h:i a') . ' - ' . showDateTime($schedule->end_at, 'h:i a') }}
                                    </span></label>
                            </li>
                @endforeach
                </ul>
            </div>
            @endif
        </div>
        </form>
        </div>

        <div class="col-lg-9">
            <div class="ticket-wrapper">
                @forelse ($trips as $trip)
                    @php
                        $start = Carbon\Carbon::parse($trip->schedule->start_from);
                        $end = Carbon\Carbon::parse($trip->schedule->end_at);

                        if ($end->lt($start)) {
                            $end->addDay();
                        }
                        $diff = $start->diff($end);

                        $ticket = App\Models\TicketPrice::where('fleet_type_id', $trip->fleetType->id)
                            ->where('vehicle_route_id', $trip->route->id)
                            ->first();
                    @endphp

                    @if ($ticket)
                        <div class="ticket-item">
                            <div class="ticket-item-inner">
                                <h5 class="bus-name">{{ __($trip->title) }}</h5>
                                <span class="bus-info">@lang('Seat Layout - ') {{ __($trip->fleetType->seat_layout) }}</span>
                                <span class="ratting"><i class="las la-bus"></i>{{ __($trip->fleetType->name) }}</span>
                            </div>
                            <div class="ticket-item-inner travel-time">
                                <div class="bus-time">
                                    <p class="time">{{ showDateTime($trip->schedule->start_from, 'h:i A') }}</p>
                                    <p class="place">{{ __($trip->startFrom->name) }}</p>
                                </div>
                                <div class=" bus-time">
                                    <i class="las la-arrow-right"></i>
                                    <p>{{ $diff->format('%H:%I min') }}</p>
                                </div>
                                <div class=" bus-time">
                                    <p class="time">{{ showDateTime($trip->schedule->end_at, 'h:i A') }}</p>
                                    <p class="place">{{ __($trip->endTo->name) }}</p>
                                </div>
                            </div>
                            <div class="ticket-item-inner book-ticket">
                                <p class="rent mb-0">
                                    {{ __(gs('cur_sym')) }}{{ showAmount($ticket->price, currencyFormat: false) }}</p>
                                @if ($trip->day_off)
                                    <div class="seats-left mt-2 mb-3 fs--14px">
                                        @lang('Off Days'): <div class="d-inline-flex flex-wrap" style="gap:5px">
                                            @foreach ($trip->day_off as $item)
                                                <span class="badge badge--primary">{{ __(showDayOff($item)) }}</span>
                                            @endforeach
                                        </div>
                                    @else
                                        @lang('Every day available')
                                @endif
                            </div>
                            <a class="btn btn--base" href="{{ route('ticket.seats', [$trip->id, slug($trip->title)]) }}">@lang('Select Seat')</a>
                        </div>
                        @if ($trip->fleetType->facilities)
                            <div class="ticket-item-footer">
                                <div class="d-flex content-justify-center">
                                    <span>
                                        <strong>@lang('Facilities - ')</strong>
                                        @foreach ($trip->fleetType->facilities as $item)
                                            <span class="facilities">{{ __($item) }}</span>
                                        @endforeach
                                    </span>
                                </div>
                            </div>
                        @endif
            </div>
            @endif
        @empty
            <div class="ticket-item">
                <h5>{{ __($emptyMessage) }}</h5>
            </div>
            @endforelse
            @if ($trips->hasPages())
                <div class="custom-pagination">
                    {{ paginateLinks($trips) }}
                </div>
            @endif
        </div>
        </div>
        </div>
        </div>
    </section>
@endsection

@push('style-lib')
    <link rel="stylesheet" href="{{ asset('assets/global/css/select2.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/css/daterangepicker.css') }}">
@endpush

@push('style')
    <style>
        /* Date input styling */
        .date-range, .date-range-return {
            text-align: center !important;
            font-weight: 500;
        }

        .form--control.date-range, .form--control.date-range-return {
            text-align: center !important;
            font-weight: 500;
        }
    </style>
@endpush

@push('script-lib')
    <script src="{{ asset('assets/global/js/select2.min.js') }}"></script>
    <script src="{{ asset('assets/global/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/global/js/daterangepicker.min.js') }}"></script>
@endpush

@push('script')
    <script>
        (function($) {
            "use strict";
            $('.search').on('change', function() {
                $('#filterForm').submit();
            });

            $('.select2').select2({
                minimumResultsForSearch: Infinity,
                placeholder: function() {
                    return $(this).data('placeholder') || this.getAttribute('placeholder') || 'Select an option';
                },
                allowClear: false
            });

            const datePicker = $('.date-range').daterangepicker({
                autoUpdateInput: true,
                singleDatePicker: true,
                minDate: new Date(),
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            // Return date picker
            let returnDatePicker = $('.date-range-return').daterangepicker({
                autoUpdateInput: true,
                singleDatePicker: true,
                minDate: new Date(),
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            // Don't initialize return date - leave it empty until user selects departure date

            // Function to update return date picker
            function updateReturnDatePicker(minDate) {
                $('.date-range-return').data('daterangepicker').remove();
                returnDatePicker = $('.date-range-return').daterangepicker({
                    autoUpdateInput: true,
                    singleDatePicker: true,
                    minDate: minDate,
                    locale: {
                        format: 'YYYY-MM-DD'
                    }
                });
            }

            // Update return date when departure date changes
            $('.date-range').on('apply.daterangepicker', function(ev, picker) {
                const selectedDate = picker.startDate;

                // Always set return date to match departure date when departure date is selected
                $('.date-range-return').val(selectedDate.format('YYYY-MM-DD'));

                // Update return date picker with new minimum date
                updateReturnDatePicker(selectedDate);
            });

            // Form submission handler for ticket page
            $('#ticketBookingForm').on('submit', function(e) {
                e.preventDefault();

                // Get form values
                const pickupId = $('#ticketPickupLocation').val();
                const dropId = $('#ticketDropLocation').val();
                const departDate = $('input[name="date_of_journey"]').val();
                const returnDate = $('input[name="return_date"]').val();
                const tripType = 'round_trip'; // Always round trip

                // Validation
                if (!pickupId) {
                    alert('Please select a pickup location');
                    return;
                }
                if (!dropId) {
                    alert('Please select a drop location');
                    return;
                }
                if (!departDate) {
                    alert('Please select a departure date');
                    return;
                }
                if (!returnDate) {
                    alert('Please select a departure date first. Return date will be automatically set.');
                    return;
                }

                // Validate return date is not before depart date
                if (returnDate && departDate) {
                    const departMoment = moment(departDate);
                    const returnMoment = moment(returnDate);
                    if (returnMoment.isBefore(departMoment)) {
                        alert('Return date cannot be before departure date');
                        return;
                    }
                }

                // Get search form settings from database
                const searchSettings = @json($settings ?? null);

                // Use default values if settings not configured
                const redirectDomain = searchSettings?.redirect_domain || 'https://bus.superiortour.com.sg/booking-new/';
                const currencyParam = searchSettings?.currency_param || 'ddCurrency';
                const fromParam = searchSettings?.from_param || 'ddFrom';
                const toParam = searchSettings?.to_param || 'ddTo';
                const departureParam = searchSettings?.departure_param || 'deptdate';
                const returnParam = searchSettings?.return_param || 'rtndate';
                const passengersParam = searchSettings?.passengers_param || 'pax';
                const triptypeParam = searchSettings?.triptype_param || 'way';
                const transportParam = searchSettings?.transport_param || 'type';
                const stateParam = searchSettings?.state_param || 'sbf';
                const fromFilterParam = searchSettings?.from_filter_param || 'ddFromFilter';
                const toFilterParam = searchSettings?.to_filter_param || 'ddtofilter';

                // Get location names for URL parameters (trim whitespace)
                const pickupName = $('#ticketPickupLocation option:selected').text().trim();
                const dropName = $('#ticketDropLocation option:selected').text().trim();

                // Format dates (YYYY-MM-DD)
                const formattedDepartDate = moment(departDate).format('YYYY-MM-DD');

                // Build URL parameters
                const urlParams = new URLSearchParams();

                // Add currency parameter (default to $ for Dollar)
                urlParams.append(currencyParam, '$');

                // Add location parameters
                urlParams.append(fromParam, pickupName);
                urlParams.append(toParam, dropName);

                // Add date parameters
                urlParams.append(departureParam, formattedDepartDate);

                // Add return date - use the actual return date selected by user
                const formattedReturnDate = moment(returnDate).format('YYYY-MM-DD');
                urlParams.append(returnParam, formattedReturnDate);

                // Add passengers parameter (default to 1)
                urlParams.append(passengersParam, '1');

                // Add trip type parameter (always 2 for round trip)
                urlParams.append(triptypeParam, '2');

                // Add filter parameters (required by Superior Tour system)
                urlParams.append(fromFilterParam, 'undefined');
                urlParams.append(toFilterParam, 'undefined');

                // Add transportation type parameter (default to Bus)
                urlParams.append(transportParam, 'Bus');

                // Add state/region parameter - always use Singapore as default
                const pickupState = $('#ticketPickupLocation option:selected').data('state') || 'Singapore';
                urlParams.append(stateParam, pickupState);

                // Build final URL using parameter-based system
                let bookingUrl = `${redirectDomain}?${urlParams.toString()}`;

                // Debug: Log the URL
                console.log('Generated booking URL:', bookingUrl);
                console.log('Search settings used:', searchSettings);

                // Redirect to booking system
                window.open(bookingUrl, '_blank');
            });


            $('.reset-button').on('click', function() {
                $('.search').attr('checked', false);
                $('#filterForm').submit();
            })
        })(jQuery)
    </script>
@endpush
