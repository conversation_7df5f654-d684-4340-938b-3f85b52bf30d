{"__meta": {"id": "01K617BNTTXZK0RD05332BCBJX", "datetime": "2025-09-25 19:59:44", "utime": **********.988366, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 16, "start": **********.127158, "end": **********.98838, "duration": 0.8612220287322998, "duration_str": "861ms", "measures": [{"label": "Booting", "start": **********.127158, "relative_start": 0, "end": **********.605843, "relative_end": **********.605843, "duration": 0.****************, "duration_str": "479ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.60586, "relative_start": 0.****************, "end": **********.988382, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.626647, "relative_start": 0.*****************, "end": **********.634989, "relative_end": **********.634989, "duration": 0.*****************, "duration_str": "8.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.697133, "relative_start": 0.****************, "end": **********.984663, "relative_end": **********.984663, "duration": 0.*****************, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: Template::home", "start": **********.699578, "relative_start": 0.****************, "end": **********.699578, "relative_end": **********.699578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.banner", "start": **********.700567, "relative_start": 0.****************, "end": **********.700567, "relative_end": **********.700567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.sections.how_it_works", "start": **********.905569, "relative_start": 0.7784111499786377, "end": **********.905569, "relative_end": **********.905569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.sections.amenities", "start": **********.913508, "relative_start": 0.7863500118255615, "end": **********.913508, "relative_end": **********.913508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.sections.testimonials", "start": **********.923137, "relative_start": 0.7959790229797363, "end": **********.923137, "relative_end": **********.923137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.frontend", "start": **********.927649, "relative_start": 0.8004910945892334, "end": **********.927649, "relative_end": **********.927649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.header", "start": **********.928198, "relative_start": 0.8010401725769043, "end": **********.928198, "relative_end": **********.928198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.footer", "start": **********.95591, "relative_start": 0.8287520408630371, "end": **********.95591, "relative_end": **********.95591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.app", "start": **********.969247, "relative_start": 0.8420891761779785, "end": **********.969247, "relative_end": **********.969247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.seo", "start": **********.973118, "relative_start": 0.8459601402282715, "end": **********.973118, "relative_end": **********.973118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.preloader", "start": **********.979642, "relative_start": 0.8524839878082275, "end": **********.979642, "relative_end": **********.979642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.982138, "relative_start": 0.8549799919128418, "end": **********.982138, "relative_end": **********.982138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 39213440, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.46.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "Template::home", "param_count": null, "params": [], "start": **********.699532, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/home.blade.phpTemplate::home", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "templates.basic.partials.banner", "param_count": null, "params": [], "start": **********.700525, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.phptemplates.basic.partials.banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "templates.basic.sections.how_it_works", "param_count": null, "params": [], "start": **********.905533, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/sections/how_it_works.blade.phptemplates.basic.sections.how_it_works", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fsections%2Fhow_it_works.blade.php&line=1", "ajax": false, "filename": "how_it_works.blade.php", "line": "?"}}, {"name": "templates.basic.sections.amenities", "param_count": null, "params": [], "start": **********.913409, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/sections/amenities.blade.phptemplates.basic.sections.amenities", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fsections%2Famenities.blade.php&line=1", "ajax": false, "filename": "amenities.blade.php", "line": "?"}}, {"name": "templates.basic.sections.testimonials", "param_count": null, "params": [], "start": **********.9231, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/sections/testimonials.blade.phptemplates.basic.sections.testimonials", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fsections%2Ftestimonials.blade.php&line=1", "ajax": false, "filename": "testimonials.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.frontend", "param_count": null, "params": [], "start": **********.927612, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/frontend.blade.phptemplates.basic.layouts.frontend", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}}, {"name": "templates.basic.partials.header", "param_count": null, "params": [], "start": **********.928161, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.phptemplates.basic.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "templates.basic.partials.footer", "param_count": null, "params": [], "start": **********.955826, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/footer.blade.phptemplates.basic.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.app", "param_count": null, "params": [], "start": **********.969187, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/app.blade.phptemplates.basic.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "partials.seo", "param_count": null, "params": [], "start": **********.973058, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/partials/seo.blade.phppartials.seo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Fpartials%2Fseo.blade.php&line=1", "ajax": false, "filename": "seo.blade.php", "line": "?"}}, {"name": "templates.basic.partials.preloader", "param_count": null, "params": [], "start": **********.979563, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/preloader.blade.phptemplates.basic.partials.preloader", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fpreloader.blade.php&line=1", "ajax": false, "filename": "preloader.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.982078, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 24, "nb_statements": 23, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00501, "accumulated_duration_str": "5.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.668335, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SiteController.php:27", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=27", "ajax": false, "filename": "SiteController.php", "line": "27"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"pages\" where \"tempname\" = 'templates.basic.' and \"slug\" = '/' limit 1", "type": "query", "params": [], "bindings": ["templates.basic.", "/"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.67867, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "SiteController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=27", "ajax": false, "filename": "SiteController.php", "line": "27"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 25.549}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'banner.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "banner.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.892718, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 25.549, "width_percent": 3.593}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'search_form.settings' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "search_form.settings"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8952332, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 29.142, "width_percent": 2.595}, {"sql": "select * from \"locations\" where \"type\" = 'pickup' and \"status\" = 1", "type": "query", "params": [], "bindings": ["pickup", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.897331, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.banner:7", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fbanner.blade.php&line=7", "ajax": false, "filename": "banner.blade.php", "line": "7"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 31.737, "width_percent": 2.994}, {"sql": "select * from \"locations\" where \"type\" = 'drop' and \"status\" = 1", "type": "query", "params": [], "bindings": ["drop", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 8}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.898626, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.banner:8", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 8}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fbanner.blade.php&line=8", "ajax": false, "filename": "banner.blade.php", "line": "8"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 34.731, "width_percent": 1.996}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'how_it_works.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "how_it_works.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.906391, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 36.727, "width_percent": 3.992}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'how_it_works.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "how_it_works.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.909358, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 40.719, "width_percent": 4.391}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'amenities.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "amenities.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9148328, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 45.11, "width_percent": 4.79}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'amenities.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "amenities.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.919178, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 49.9, "width_percent": 4.79}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'testimonials.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "testimonials.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.924086, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 54.691, "width_percent": 2.794}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'testimonials.element' order by \"id\" desc", "type": "query", "params": [], "bindings": ["basic", "testimonials.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 383}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9257102, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "helpers.php:383", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 383}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=383", "ajax": false, "filename": "helpers.php", "line": "383"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 57.485, "width_percent": 1.796}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'contact.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "contact.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9289892, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 59.281, "width_percent": 1.996}, {"sql": "select * from \"languages\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 3}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.933228, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.header:3", "source": {"index": 16, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 3}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=3", "ajax": false, "filename": "header.blade.php", "line": "3"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 61.277, "width_percent": 3.393}, {"sql": "select * from \"pages\" where \"tempname\" = 'templates.basic.' and \"is_default\" = 0", "type": "query", "params": [], "bindings": ["templates.basic.", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.936893, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.header:7", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=7", "ajax": false, "filename": "header.blade.php", "line": "7"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 64.671, "width_percent": 7.186}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'footer.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "footer.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.957447, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 71.856, "width_percent": 3.593}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'social_links.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "social_links.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.959252, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 75.449, "width_percent": 2.196}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'policy_pages.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "policy_pages.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9607332, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 77.645, "width_percent": 2.595}, {"sql": "select * from \"pages\" where \"tempname\" = 'templates.basic.' and \"is_default\" = 0", "type": "query", "params": [], "bindings": ["templates.basic.", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.footer", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/footer.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.962292, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.footer:7", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.footer", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/footer.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Ffooter.blade.php&line=7", "ajax": false, "filename": "footer.blade.php", "line": "7"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 80.24, "width_percent": 1.397}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'contact.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "contact.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.964494, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 81.637, "width_percent": 1.996}, {"sql": "select * from \"frontends\" where \"data_keys\" = 'cookie.data' limit 1", "type": "query", "params": [], "bindings": ["cookie.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.layouts.frontend", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/frontend.blade.php", "line": 14}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.966775, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "templates.basic.layouts.frontend:14", "source": {"index": 16, "namespace": "view", "name": "templates.basic.layouts.frontend", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/frontend.blade.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Ffrontend.blade.php&line=14", "ajax": false, "filename": "frontend.blade.php", "line": "14"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 83.633, "width_percent": 4.591}, {"sql": "select * from \"frontends\" where \"data_keys\" = 'seo.data' limit 1", "type": "query", "params": [], "bindings": ["seo.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Providers\\AppServiceProvider.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/app.blade.php", "line": 9}], "start": **********.970579, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:72", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Providers\\AppServiceProvider.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FProviders%2FAppServiceProvider.php&line=72", "ajax": false, "filename": "AppServiceProvider.php", "line": "72"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 88.224, "width_percent": 3.992}, {"sql": "select * from \"extensions\" where \"act\" = 'google-analytics' and \"status\" = 1 limit 1", "type": "query", "params": [], "bindings": ["google-analytics", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.976767, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "helpers.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=106", "ajax": false, "filename": "helpers.php", "line": "106"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 92.216, "width_percent": 3.992}, {"sql": "select * from \"extensions\" where \"act\" = 'tawk-chat' and \"status\" = 1 limit 1", "type": "query", "params": [], "bindings": ["tawk-chat", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.980677, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=106", "ajax": false, "filename": "helpers.php", "line": "106"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 96.208, "width_percent": 3.792}]}, "models": {"data": {"App\\Models\\Frontend": {"retrieved": 17, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FFrontend.php&line=1", "ajax": false, "filename": "Frontend.php", "line": "?"}}, "App\\Models\\Location": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "App\\Models\\Page": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "App\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 21, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 21}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\SiteController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\SiteController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SiteController.php:23-53</a>", "middleware": "web, maintenance", "duration": "866ms", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-648244037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-648244037\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1466005744 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1466005744\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-935532018 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"673 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkxXT1RRYU1jR3lOa1VITG54dWw3cXc9PSIsInZhbHVlIjoiaGVrK2NSRFF1OUt0aDhDZFBmb2FScWNnekx1TVhuTXZ2RS9CaVIySHJ2RHloVlNWdXJXbFBjVUE1QWdicE9DeWNtdFdNeWJHQ21nTDhaelN6TW1UWVBra2RDYmFDb1grcndVWEhscitvbnBlOXNGZVlBdFhFMUt2MUFERDBEM2JnT2JtemtnS2EzdjhxU2tHUkp5NTQxQ01jajFCQmd5dlBiN1BXOWZrTm0vL2xwTWc1TzlFWDRFeEtjaWMycFFQbjRMSXJzK1VtTXBvekhaVHYycjNGRlMxTVJXSVhPcjZmQVpmb29pR01XWT0iLCJtYWMiOiIwMDNkOGJkMGI5OGVlZGI4ZTUzYzJhNGY4YjE5NWIwM2I5MjY5MzEwN2E0MWYzOTZjYjNjNzM0NWEzMGU0MmJjIiwidGFnIjoiIn0%3D; gdpr_cookie=ViserBus; XSRF-TOKEN=fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG; laravel_session=eZODaKYBsBUaMRrV23dUM2avaivE6JJ4wfubNsMM</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935532018\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1465834468 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IkxXT1RRYU1jR3lOa1VITG54dWw3cXc9PSIsInZhbHVlIjoiaGVrK2NSRFF1OUt0aDhDZFBmb2FScWNnekx1TVhuTXZ2RS9CaVIySHJ2RHloVlNWdXJXbFBjVUE1QWdicE9DeWNtdFdNeWJHQ21nTDhaelN6TW1UWVBra2RDYmFDb1grcndVWEhscitvbnBlOXNGZVlBdFhFMUt2MUFERDBEM2JnT2JtemtnS2EzdjhxU2tHUkp5NTQxQ01jajFCQmd5dlBiN1BXOWZrTm0vL2xwTWc1TzlFWDRFeEtjaWMycFFQbjRMSXJzK1VtTXBvekhaVHYycjNGRlMxTVJXSVhPcjZmQVpmb29pR01XWT0iLCJtYWMiOiIwMDNkOGJkMGI5OGVlZGI4ZTUzYzJhNGY4YjE5NWIwM2I5MjY5MzEwN2E0MWYzOTZjYjNjNzM0NWEzMGU0MmJjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => \"<span class=sf-dump-str title=\"8 characters\">ViserBus</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eZODaKYBsBUaMRrV23dUM2avaivE6JJ4wfubNsMM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465834468\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1169410542 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 19:59:44 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169410542\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1240900851 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/placeholder-image/50x50</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240900851\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\SiteController@index"}, "badge": null}}