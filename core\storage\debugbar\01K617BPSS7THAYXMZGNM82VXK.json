{"__meta": {"id": "01K617BPSS7THAYXMZGNM82VXK", "datetime": "2025-09-25 19:59:45", "utime": **********.977779, "method": "GET", "uri": "/placeholder-image/50x50", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[19:59:45] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php on line 442", "message_html": null, "is_string": false, "label": "warning", "time": **********.972204, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 4, "start": **********.647682, "end": **********.977802, "duration": 0.3301200866699219, "duration_str": "330ms", "measures": [{"label": "Booting", "start": **********.647682, "relative_start": 0, "end": **********.938784, "relative_end": **********.938784, "duration": 0.*****************, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.938801, "relative_start": 0.****************, "end": **********.977804, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.954448, "relative_start": 0.****************, "end": **********.959549, "relative_end": **********.959549, "duration": 0.*****************, "duration_str": "5.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.972681, "relative_start": 0.****************, "end": **********.97539, "relative_end": **********.97539, "duration": 0.002708911895751953, "duration_str": "2.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.46.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/placeholder-image/50x50", "action_name": "placeholder.image", "controller_action": "App\\Http\\Controllers\\SiteController@placeholderImage", "uri": "GET placeholder-image/{size}", "controller": "App\\Http\\Controllers\\SiteController@placeholderImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=418\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "excluded_middleware": ["maintenance"], "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=418\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SiteController.php:418-445</a>", "middleware": "web, maintenance", "duration": "331ms", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1752325837 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1752325837\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-407048104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-407048104\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1495975262 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"673 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkxXT1RRYU1jR3lOa1VITG54dWw3cXc9PSIsInZhbHVlIjoiaGVrK2NSRFF1OUt0aDhDZFBmb2FScWNnekx1TVhuTXZ2RS9CaVIySHJ2RHloVlNWdXJXbFBjVUE1QWdicE9DeWNtdFdNeWJHQ21nTDhaelN6TW1UWVBra2RDYmFDb1grcndVWEhscitvbnBlOXNGZVlBdFhFMUt2MUFERDBEM2JnT2JtemtnS2EzdjhxU2tHUkp5NTQxQ01jajFCQmd5dlBiN1BXOWZrTm0vL2xwTWc1TzlFWDRFeEtjaWMycFFQbjRMSXJzK1VtTXBvekhaVHYycjNGRlMxTVJXSVhPcjZmQVpmb29pR01XWT0iLCJtYWMiOiIwMDNkOGJkMGI5OGVlZGI4ZTUzYzJhNGY4YjE5NWIwM2I5MjY5MzEwN2E0MWYzOTZjYjNjNzM0NWEzMGU0MmJjIiwidGFnIjoiIn0%3D; gdpr_cookie=ViserBus; XSRF-TOKEN=fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG; laravel_session=eZODaKYBsBUaMRrV23dUM2avaivE6JJ4wfubNsMM</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495975262\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-397457276 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IkxXT1RRYU1jR3lOa1VITG54dWw3cXc9PSIsInZhbHVlIjoiaGVrK2NSRFF1OUt0aDhDZFBmb2FScWNnekx1TVhuTXZ2RS9CaVIySHJ2RHloVlNWdXJXbFBjVUE1QWdicE9DeWNtdFdNeWJHQ21nTDhaelN6TW1UWVBra2RDYmFDb1grcndVWEhscitvbnBlOXNGZVlBdFhFMUt2MUFERDBEM2JnT2JtemtnS2EzdjhxU2tHUkp5NTQxQ01jajFCQmd5dlBiN1BXOWZrTm0vL2xwTWc1TzlFWDRFeEtjaWMycFFQbjRMSXJzK1VtTXBvekhaVHYycjNGRlMxTVJXSVhPcjZmQVpmb29pR01XWT0iLCJtYWMiOiIwMDNkOGJkMGI5OGVlZGI4ZTUzYzJhNGY4YjE5NWIwM2I5MjY5MzEwN2E0MWYzOTZjYjNjNzM0NWEzMGU0MmJjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => \"<span class=sf-dump-str title=\"8 characters\">ViserBus</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eZODaKYBsBUaMRrV23dUM2avaivE6JJ4wfubNsMM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-397457276\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1844903878 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 19:59:45 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844903878\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-809274497 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809274497\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/placeholder-image/50x50", "action_name": "placeholder.image", "controller_action": "App\\Http\\Controllers\\SiteController@placeholderImage"}, "badge": null}}