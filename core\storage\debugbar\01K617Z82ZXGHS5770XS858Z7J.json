{"__meta": {"id": "01K617Z82ZXGHS5770XS858Z7J", "datetime": "2025-09-25 20:10:26", "utime": **********.273092, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 16, "start": 1758831025.576653, "end": **********.273119, "duration": 0.6964659690856934, "duration_str": "696ms", "measures": [{"label": "Booting", "start": 1758831025.576653, "relative_start": 0, "end": **********.013019, "relative_end": **********.013019, "duration": 0.*****************, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.013027, "relative_start": 0.****************, "end": **********.273123, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.043672, "relative_start": 0.*****************, "end": **********.058202, "relative_end": **********.058202, "duration": 0.014529943466186523, "duration_str": "14.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.155488, "relative_start": 0.****************, "end": **********.267243, "relative_end": **********.267243, "duration": 0.****************, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: Template::home", "start": **********.157886, "relative_start": 0.***************, "end": **********.157886, "relative_end": **********.157886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.banner", "start": **********.158509, "relative_start": 0.****************, "end": **********.158509, "relative_end": **********.158509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.sections.how_it_works", "start": **********.186472, "relative_start": 0.6098189353942871, "end": **********.186472, "relative_end": **********.186472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.sections.amenities", "start": **********.195128, "relative_start": 0.6184749603271484, "end": **********.195128, "relative_end": **********.195128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.sections.testimonials", "start": **********.206965, "relative_start": 0.6303119659423828, "end": **********.206965, "relative_end": **********.206965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.frontend", "start": **********.214124, "relative_start": 0.6374709606170654, "end": **********.214124, "relative_end": **********.214124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.header", "start": **********.214992, "relative_start": 0.6383390426635742, "end": **********.214992, "relative_end": **********.214992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.footer", "start": **********.232456, "relative_start": 0.6558029651641846, "end": **********.232456, "relative_end": **********.232456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.app", "start": **********.250055, "relative_start": 0.6734020709991455, "end": **********.250055, "relative_end": **********.250055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.seo", "start": **********.253445, "relative_start": 0.6767919063568115, "end": **********.253445, "relative_end": **********.253445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.preloader", "start": **********.261433, "relative_start": 0.6847798824310303, "end": **********.261433, "relative_end": **********.261433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.263366, "relative_start": 0.6867129802703857, "end": **********.263366, "relative_end": **********.263366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 39088216, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.46.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "Template::home", "param_count": null, "params": [], "start": **********.157832, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/home.blade.phpTemplate::home", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "templates.basic.partials.banner", "param_count": null, "params": [], "start": **********.158472, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.phptemplates.basic.partials.banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "templates.basic.sections.how_it_works", "param_count": null, "params": [], "start": **********.186381, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/sections/how_it_works.blade.phptemplates.basic.sections.how_it_works", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fsections%2Fhow_it_works.blade.php&line=1", "ajax": false, "filename": "how_it_works.blade.php", "line": "?"}}, {"name": "templates.basic.sections.amenities", "param_count": null, "params": [], "start": **********.195056, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/sections/amenities.blade.phptemplates.basic.sections.amenities", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fsections%2Famenities.blade.php&line=1", "ajax": false, "filename": "amenities.blade.php", "line": "?"}}, {"name": "templates.basic.sections.testimonials", "param_count": null, "params": [], "start": **********.20686, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/sections/testimonials.blade.phptemplates.basic.sections.testimonials", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fsections%2Ftestimonials.blade.php&line=1", "ajax": false, "filename": "testimonials.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.frontend", "param_count": null, "params": [], "start": **********.214064, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/frontend.blade.phptemplates.basic.layouts.frontend", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}}, {"name": "templates.basic.partials.header", "param_count": null, "params": [], "start": **********.214919, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.phptemplates.basic.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "templates.basic.partials.footer", "param_count": null, "params": [], "start": **********.232366, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/footer.blade.phptemplates.basic.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.app", "param_count": null, "params": [], "start": **********.249975, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/app.blade.phptemplates.basic.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "partials.seo", "param_count": null, "params": [], "start": **********.253407, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/partials/seo.blade.phppartials.seo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Fpartials%2Fseo.blade.php&line=1", "ajax": false, "filename": "seo.blade.php", "line": "?"}}, {"name": "templates.basic.partials.preloader", "param_count": null, "params": [], "start": **********.2614, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/preloader.blade.phptemplates.basic.partials.preloader", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fpreloader.blade.php&line=1", "ajax": false, "filename": "preloader.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.263339, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 24, "nb_statements": 23, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00648, "accumulated_duration_str": "6.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.12349, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SiteController.php:27", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=27", "ajax": false, "filename": "SiteController.php", "line": "27"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"pages\" where \"tempname\" = 'templates.basic.' and \"slug\" = '/' limit 1", "type": "query", "params": [], "bindings": ["templates.basic.", "/"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.1409621, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "SiteController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Controllers\\SiteController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=27", "ajax": false, "filename": "SiteController.php", "line": "27"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 21.296}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'banner.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "banner.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.1608129, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 21.296, "width_percent": 3.704}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'search_form.settings' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "search_form.settings"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.167028, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 25, "width_percent": 8.025}, {"sql": "select * from \"locations\" where \"type\" = 'pickup' and \"status\" = 1", "type": "query", "params": [], "bindings": ["pickup", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.170021, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.banner:7", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fbanner.blade.php&line=7", "ajax": false, "filename": "banner.blade.php", "line": "7"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 33.025, "width_percent": 2.623}, {"sql": "select * from \"locations\" where \"type\" = 'drop' and \"status\" = 1", "type": "query", "params": [], "bindings": ["drop", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 8}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.1743891, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.banner:8", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.banner", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/banner.blade.php", "line": 8}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fbanner.blade.php&line=8", "ajax": false, "filename": "banner.blade.php", "line": "8"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 35.648, "width_percent": 4.784}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'how_it_works.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "how_it_works.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.187577, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 40.432, "width_percent": 3.086}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'how_it_works.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "how_it_works.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.19048, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 43.519, "width_percent": 2.778}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'amenities.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "amenities.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.196245, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 46.296, "width_percent": 2.315}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'amenities.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "amenities.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.199794, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 48.611, "width_percent": 7.716}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'testimonials.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "testimonials.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.208992, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 56.327, "width_percent": 5.401}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'testimonials.element' order by \"id\" desc", "type": "query", "params": [], "bindings": ["basic", "testimonials.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 383}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2111452, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "helpers.php:383", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 383}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=383", "ajax": false, "filename": "helpers.php", "line": "383"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 61.728, "width_percent": 2.006}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'contact.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "contact.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.216395, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 63.735, "width_percent": 3.549}, {"sql": "select * from \"languages\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 3}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.219124, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.header:3", "source": {"index": 16, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 3}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=3", "ajax": false, "filename": "header.blade.php", "line": "3"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 67.284, "width_percent": 2.315}, {"sql": "select * from \"pages\" where \"tempname\" = 'templates.basic.' and \"is_default\" = 0", "type": "query", "params": [], "bindings": ["templates.basic.", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2206762, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.header:7", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/header.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=7", "ajax": false, "filename": "header.blade.php", "line": "7"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 69.599, "width_percent": 1.543}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'footer.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "footer.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2340372, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 71.142, "width_percent": 4.321}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'social_links.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "social_links.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.236949, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 75.463, "width_percent": 2.778}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'policy_pages.element' order by \"id\" asc", "type": "query", "params": [], "bindings": ["basic", "policy_pages.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.239732, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "helpers.php:381", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=381", "ajax": false, "filename": "helpers.php", "line": "381"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 78.241, "width_percent": 2.006}, {"sql": "select * from \"pages\" where \"tempname\" = 'templates.basic.' and \"is_default\" = 0", "type": "query", "params": [], "bindings": ["templates.basic.", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.footer", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/footer.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2411141, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.footer:7", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.footer", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/partials/footer.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Ffooter.blade.php&line=7", "ajax": false, "filename": "footer.blade.php", "line": "7"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 80.247, "width_percent": 1.543}, {"sql": "select * from \"frontends\" where \"tempname\" = 'basic' and \"data_keys\" = 'contact.content' order by \"id\" desc limit 1", "type": "query", "params": [], "bindings": ["basic", "contact.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.243624, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "helpers.php:374", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 374}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=374", "ajax": false, "filename": "helpers.php", "line": "374"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 81.79, "width_percent": 2.16}, {"sql": "select * from \"frontends\" where \"data_keys\" = 'cookie.data' limit 1", "type": "query", "params": [], "bindings": ["cookie.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.layouts.frontend", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/frontend.blade.php", "line": 14}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.246399, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "templates.basic.layouts.frontend:14", "source": {"index": 16, "namespace": "view", "name": "templates.basic.layouts.frontend", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/frontend.blade.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Ffrontend.blade.php&line=14", "ajax": false, "filename": "frontend.blade.php", "line": "14"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 83.951, "width_percent": 3.395}, {"sql": "select * from \"frontends\" where \"data_keys\" = 'seo.data' limit 1", "type": "query", "params": [], "bindings": ["seo.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Providers\\AppServiceProvider.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\resources\\views/templates/basic/layouts/app.blade.php", "line": 9}], "start": **********.251785, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:72", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Providers\\AppServiceProvider.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FProviders%2FAppServiceProvider.php&line=72", "ajax": false, "filename": "AppServiceProvider.php", "line": "72"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 87.346, "width_percent": 2.315}, {"sql": "select * from \"extensions\" where \"act\" = 'google-analytics' and \"status\" = 1 limit 1", "type": "query", "params": [], "bindings": ["google-analytics", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.257681, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "helpers.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=106", "ajax": false, "filename": "helpers.php", "line": "106"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 89.66, "width_percent": 8.951}, {"sql": "select * from \"extensions\" where \"act\" = 'tawk-chat' and \"status\" = 1 limit 1", "type": "query", "params": [], "bindings": ["tawk-chat", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2620041, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "helpers.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\viserbus\\Files\\core\\app\\Http\\Helpers\\helpers.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=106", "ajax": false, "filename": "helpers.php", "line": "106"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 98.611, "width_percent": 1.389}]}, "models": {"data": {"App\\Models\\Frontend": {"retrieved": 17, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FFrontend.php&line=1", "ajax": false, "filename": "Frontend.php", "line": "?"}}, "App\\Models\\Location": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "App\\Models\\Page": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "App\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 21, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 21}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\SiteController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\SiteController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fraime%2FDesktop%2Fviserbus%2FFiles%2Fcore%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SiteController.php:23-53</a>", "middleware": "web, maintenance", "duration": "707ms", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2096865264 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2096865264\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-179964278 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-179964278\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2045692948 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"673 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkxXT1RRYU1jR3lOa1VITG54dWw3cXc9PSIsInZhbHVlIjoiaGVrK2NSRFF1OUt0aDhDZFBmb2FScWNnekx1TVhuTXZ2RS9CaVIySHJ2RHloVlNWdXJXbFBjVUE1QWdicE9DeWNtdFdNeWJHQ21nTDhaelN6TW1UWVBra2RDYmFDb1grcndVWEhscitvbnBlOXNGZVlBdFhFMUt2MUFERDBEM2JnT2JtemtnS2EzdjhxU2tHUkp5NTQxQ01jajFCQmd5dlBiN1BXOWZrTm0vL2xwTWc1TzlFWDRFeEtjaWMycFFQbjRMSXJzK1VtTXBvekhaVHYycjNGRlMxTVJXSVhPcjZmQVpmb29pR01XWT0iLCJtYWMiOiIwMDNkOGJkMGI5OGVlZGI4ZTUzYzJhNGY4YjE5NWIwM2I5MjY5MzEwN2E0MWYzOTZjYjNjNzM0NWEzMGU0MmJjIiwidGFnIjoiIn0%3D; gdpr_cookie=ViserBus; XSRF-TOKEN=fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG; laravel_session=eZODaKYBsBUaMRrV23dUM2avaivE6JJ4wfubNsMM</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045692948\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1618431239 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IkxXT1RRYU1jR3lOa1VITG54dWw3cXc9PSIsInZhbHVlIjoiaGVrK2NSRFF1OUt0aDhDZFBmb2FScWNnekx1TVhuTXZ2RS9CaVIySHJ2RHloVlNWdXJXbFBjVUE1QWdicE9DeWNtdFdNeWJHQ21nTDhaelN6TW1UWVBra2RDYmFDb1grcndVWEhscitvbnBlOXNGZVlBdFhFMUt2MUFERDBEM2JnT2JtemtnS2EzdjhxU2tHUkp5NTQxQ01jajFCQmd5dlBiN1BXOWZrTm0vL2xwTWc1TzlFWDRFeEtjaWMycFFQbjRMSXJzK1VtTXBvekhaVHYycjNGRlMxTVJXSVhPcjZmQVpmb29pR01XWT0iLCJtYWMiOiIwMDNkOGJkMGI5OGVlZGI4ZTUzYzJhNGY4YjE5NWIwM2I5MjY5MzEwN2E0MWYzOTZjYjNjNzM0NWEzMGU0MmJjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => \"<span class=sf-dump-str title=\"8 characters\">ViserBus</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eZODaKYBsBUaMRrV23dUM2avaivE6JJ4wfubNsMM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618431239\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2073228009 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 20:10:26 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073228009\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-348266128 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fytCFloNrhVuHXJAn6UiyKSH5b024TD9ZvKP7qiG</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/placeholder-image/50x50</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348266128\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\SiteController@index"}, "badge": null}}