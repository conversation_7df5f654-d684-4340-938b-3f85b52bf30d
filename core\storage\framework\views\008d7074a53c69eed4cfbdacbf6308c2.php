<?php $__env->startSection('content'); ?>
    <div class="mb-4">
        <p><?php echo app('translator')->get('Your account is verified successfully. Now you can change your password. Please enter a strong password and don\'t share it with anyone.'); ?></p>
    </div>
    <form method="POST" action="<?php echo e(route('user.password.update')); ?>">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="email" value="<?php echo e($email); ?>">
        <input type="hidden" name="token" value="<?php echo e($token); ?>">
        <div class="row gy-3">
            <div class="form-group col-xl-12">
                <label class="form-label"><?php echo app('translator')->get('Password'); ?></label>
                <input type="password" class="form-control form--control <?php if(gs('secure_password')): ?> secure-password <?php endif; ?>" name="password" required>
            </div>
            <div class="form-group col-xl-12">
                <label class="form-label"><?php echo app('translator')->get('Confirm Password'); ?></label>
                <input type="password" class="form-control form--control" name="password_confirmation" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn--base w-100"> <?php echo app('translator')->get('Submit'); ?></button>
            </div>
        </div>
    </form>
<?php $__env->stopSection(); ?>
<?php if(gs('secure_password')): ?>
    <?php $__env->startPush('script-lib'); ?>
        <script src="<?php echo e(asset('assets/global/js/secure_password.js')); ?>"></script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>

<?php echo $__env->make($activeTemplate . 'layouts.authenticate', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\auth\passwords\reset.blade.php ENDPATH**/ ?>