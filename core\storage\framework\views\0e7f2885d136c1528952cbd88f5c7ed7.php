<?php $__env->startSection('content'); ?>
<div class="container padding-top padding-bottom">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card cmn--card card-deposit">
                <div class="card-header card-header-bg">
                    <h5 class="title text-center"><?php echo e(__($pageTitle)); ?></h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('user.deposit.manual.update')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <p class="text-center mt-2"><?php echo app('translator')->get('You have requested'); ?> <b class="text-success"><?php echo e(showAmount($data['amount'])); ?></b> , <?php echo app('translator')->get('Please pay'); ?>
                                    <b class="text-success"><?php echo e(showAmount($data['final_amount']) .' '.$data['method_currency']); ?> </b> <?php echo app('translator')->get('for successful payment'); ?>
                                </p>
                                <h4 class="text-center mt-3 mb-4"><?php echo app('translator')->get('Please follow the instruction below'); ?></h4>

                                <p class="my-4 text-center"><?php echo $data->gateway->description ?></p>

                            </div>

                            <?php if($method->gateway_parameter): ?>

                            <?php $__currentLoopData = json_decode($method->gateway_parameter); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                            <?php if($v->type == "text"): ?>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label text-left"><strong><?php echo e(__(inputTitle($v->field_level))); ?> <?php if($v->validation == 'required'): ?> <span class="text-danger">*</span> <?php endif; ?></strong></label>
                                    <input type="text" class="form--control" name="<?php echo e($k); ?>" value="<?php echo e(old($k)); ?>" placeholder="<?php echo e(__($v->field_level)); ?>">
                                </div>
                            </div>
                            <?php elseif($v->type == "textarea"): ?>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label"><strong><?php echo e(__(inputTitle($v->field_level))); ?> <?php if($v->validation == 'required'): ?> <span class="text-danger">*</span> <?php endif; ?></strong></label>
                                    <textarea name="<?php echo e($k); ?>" class="form-control" placeholder="<?php echo e(__($v->field_level)); ?>" rows="3"><?php echo e(old($k)); ?></textarea>

                                </div>
                            </div>
                            <?php elseif($v->type == "file"): ?>
                            <div class="col-md-12">
                                <div class="form-group mt-4">
                                    <label><strong><?php echo e(__($v->field_level)); ?> <?php if($v->validation == 'required'): ?> <span class="text-danger">*</span> <?php endif; ?></strong></label>
                                    <br>

                                    <div class="fileinput fileinput-new " data-provides="fileinput">
                                        <div class="fileinput-new thumbnail withdraw-thumbnail" data-trigger="fileinput">
                                            <img src="<?php echo e(asset(getImage('/'))); ?>" alt="<?php echo app('translator')->get('Image'); ?>" id="img-preview">
                                        </div>
                                        <div class="fileinput-preview fileinput-exists thumbnail wh-200-150"></div>

                                        <div class="img-input-div">
                                            <span class="btn-file">
                                                <span class="fileinput-new btn btn-primary"> <?php echo app('translator')->get('Select'); ?> <?php echo e(__($v->field_level)); ?></span>
                                                <span class="fileinput-exists"> <?php echo app('translator')->get('Change'); ?></span>
                                                <input class="btn btn--info" type="file" name="<?php echo e($k); ?>" accept="image/*" onchange="document.getElementById('img-preview').src = window.URL.createObjectURL(this.files[0])">
                                            </span>
                                            <a href="#" class="btn btn--danger fileinput-exists" data-dismiss="fileinput"> <?php echo app('translator')->get('Remove'); ?></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <button type="submit" class="btn btn--base mt-4 h-40"><?php echo app('translator')->get('Pay Now'); ?></button>
                                </div>
                            </div>

                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('style'); ?>
<style>
    .withdraw-thumbnail {
        max-width: 220px;
        max-height: 220px
    }
</style>

<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-lib'); ?>
<script src="<?php echo e(asset($activeTemplateTrue.'/js/bootstrap-fileinput.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('style-lib'); ?>
<link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue.'/css/bootstrap-fileinput.css')); ?>">
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate.'layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\manual_payment\manual_confirm.blade.php ENDPATH**/ ?>