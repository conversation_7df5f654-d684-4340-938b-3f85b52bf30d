<?php $__env->startSection('panel'); ?>
    <div class="row mb-none-30">
        <div class="col-xl-12 col-lg-12 col-md-12 mb-30">
            <div class="card">
                <h5 class="card-header"><?php echo app('translator')->get('Information of Route'); ?> </h5>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.trip.route.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Name'); ?></label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label> <?php echo app('translator')->get('Start From'); ?></label>
                                    <select name="start_from" class="select2" required>
                                        <option value=""><?php echo app('translator')->get('Select an option'); ?></option>
                                        <?php $__currentLoopData = $stoppages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item->id); ?>"><?php echo e(__($item->name)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label> <?php echo app('translator')->get('End To'); ?></label>
                                    <select name="end_to" class="select2" required>
                                        <option value=""><?php echo app('translator')->get('Select an option'); ?></option>
                                        <?php $__currentLoopData = $stoppages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item->id); ?>"><?php echo e(__($item->name)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox form-check-primary">
                                        <input type="checkbox" class="custom-control-input" id="has-stoppage">
                                        <label class="custom-control-label" for="has-stoppage"><?php echo app('translator')->get('Has More Stoppage'); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="stoppages-wrapper col-md-12 d-none"></div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label> <?php echo app('translator')->get('Time'); ?></label>
                                    <input type="text" class="form-control" name="time" placeholder="<?php echo app('translator')->get('Enter Approximate Time'); ?>" required>
                                    <small class="text--info text--small"><i class="fas fa-info-circle"></i> <?php echo app('translator')->get('Keep space between value & unit'); ?></small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label> <?php echo app('translator')->get('Distance'); ?></label>
                                    <input type="text" class="form-control" name="distance" required>
                                    <small class="text--info text--small"><i class="fas fa-info-circle"></i> <?php echo app('translator')->get('Keep space between value & unit'); ?></small>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
    <?php if (isset($component)) { $__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.back','data' => ['route' => ''.e(route('admin.trip.route.index')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('back'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('admin.trip.route.index')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5)): ?>
<?php $attributes = $__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5; ?>
<?php unset($__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5)): ?>
<?php $component = $__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5; ?>
<?php unset($__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5); ?>
<?php endif; ?>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        "use strict";

        (function($) {
            $('#has-stoppage').on('click', function() {
                if (this.checked) {
                    var html = `<div class="row stoppages-row">
                                    <div class="col-xxl-3 col-md-6">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <span class="input-group-text serial">1</span>
                                                <select class="select2-basic" name="stoppages[1]" required>
                                                    <option value="" selected><?php echo app('translator')->get('Select Stoppage'); ?></option>
                                                    <?php $__currentLoopData = $stoppages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stoppage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($stoppage->id); ?>"><?php echo e($stoppage->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <button type="button" class="input-group-text btn btn--danger remove-stoppage"><i class="las la-times"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn--success add-stoppage-btn mb-1"><i class="la la-plus"></i><?php echo app('translator')->get('Next Stoppage'); ?></button> <br>
                                <span class="text--danger text--small"><i class="fas fa-exclamation-triangle"></i> <?php echo app('translator')->get('Make sure that you are adding stoppages serially followed by the starting point'); ?></span>`;
                    $('.stoppages-wrapper').prepend(html).removeClass('d-none');

                    initializeSelect2();
                } else {
                    itr = 2;
                    $('.stoppages-wrapper').html('').addClass('d-none');
                }
            });

            var itr = 2;
            $(document).on('click', '.add-stoppage-btn', function() {
                var option = `<div class="col-xxl-3 col-md-6">
                            <div class="form-group">
                                <div class="input-group">
                                    <span class="input-group-text serial">${itr}</span>
                                    <select class="select2-basic" name="stoppages[${itr}]">
                                        <option value="" selected><?php echo app('translator')->get('Select Stoppage'); ?></option>
                                        <?php $__currentLoopData = $stoppages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stoppage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($stoppage->id); ?>"><?php echo e($stoppage->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <button type="button" class="input-group-text btn btn--danger remove-stoppage"><i class="las la-times me-0"></i></button>
                                </div>
                                </div>
                            </div>`;

                $('.stoppages-row').append(option);
                initializeSelect2();
                itr++;
            });

            $(document).on('click', '.remove-stoppage', function() {
                $(this).closest('.col-md-3').remove();
                var elements = $('.stoppages-row .col-md-3').find();

                $($('.stoppages-row .col-md-3')).each(function(index, element) {
                    $(element).find('.input-group .serial').text(index + 1);
                    $(element).find('.select2').attr('name', `stoppages[${index+1}]`);

                });
            });

            function initializeSelect2() {
                $.each($('.select2-basic'), function() {
                    $(this)
                        .wrap(`<div class="position-relative flex-grow-1"></div>`)
                        .select2({
                            dropdownParent: $(this).parent()
                        });
                });
            }
        })(jQuery)
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        .input-group .select2-container--default .select2-selection--single {
            border-radius: 0 !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\trip\route\create.blade.php ENDPATH**/ ?>