<?php $__env->startSection('content'); ?>
    <div class="container padding-top padding-bottom">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <img src="<?php echo e($deposit->gatewayCurrency()->methodImage()); ?>" class="card-img-top" alt="<?php echo app('translator')->get('Image'); ?>" class="w-100">
                    </div>
                    <div class="col-md-8 text-center">
                        <h3 class="mt-4"><?php echo app('translator')->get('Please Pay'); ?> <?php echo e(showAmount($deposit->final_amo)); ?> <?php echo e(__($deposit->method_currency)); ?></h3>
                        <h3 class="my-3"><?php echo app('translator')->get('To Get'); ?> <?php echo e(showAmount($deposit->amount)); ?></h3>
                        <button type="button" class=" mt-4 btn-success btn-round custom-success text-center btn-lg" id="btn-confirm"><?php echo app('translator')->get('Pay Now'); ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script'); ?>
    <script src="//pay.voguepay.com/js/voguepay.js"></script>
    <script>
        "use strict";
        var closedFunction = function() {
        }
        var successFunction = function(transaction_id) {
            window.location.href = '<?php echo e(route(gatewayRedirectUrl())); ?>';
        }
        var failedFunction=function(transaction_id) {
            window.location.href = '<?php echo e(route(gatewayRedirectUrl())); ?>' ;
        }

        function pay(item, price) {
            //Initiate voguepay inline payment
            Voguepay.init({
                v_merchant_id: "<?php echo e($data->v_merchant_id); ?>",
                total: price,
                notify_url: "<?php echo e($data->notify_url); ?>",
                cur: "<?php echo e($data->cur); ?>",
                merchant_ref: "<?php echo e($data->merchant_ref); ?>",
                memo:"<?php echo e($data->memo); ?>",
                recurrent: true,
                frequency: 10,
                developer_code: '60a4ecd9bbc77',
                custom: "<?php echo e($data->custom); ?>",
                customer: {
                  name: 'Customer name',
                  country: 'Country',
                  address: 'Customer address',
                  city: 'Customer city',
                  state: 'Customer state',
                  zipcode: 'Customer zip/post code',
                  email: '<EMAIL>',
                  phone: 'Customer phone'
                },
                closed:closedFunction,
                success:successFunction,
                failed:failedFunction
            });
        }

        (function ($) {

            $('#btn-confirm').on('click', function (e) {
                e.preventDefault();
                pay('Buy', <?php echo e($data->Buy); ?>);
            });

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate.'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\payment\Voguepay.blade.php ENDPATH**/ ?>