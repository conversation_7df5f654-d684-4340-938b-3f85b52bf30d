<?php $__env->startSection('content'); ?>

<section class="blog-details padding-top padding-bottom">
	<div class="container">
		<div class="row gy-5">
			<div class="col-lg-8">
				<div class="post-thumb">
					<img src="<?php echo e(frontendImage('blog',@$blog->data_values->image)); ?>" alt="<?php echo e(__(@$blog->data_values->title)); ?>">
				</div>
				<div class="post-details-content">
					<div class="content-inner">
						<ul class="meta-post">
							<li>
								<i class="las la-calendar-check"></i>
								<span><?php echo e(showDateTime($blog->created_at, 'd M Y')); ?></span>
							</li>
						</ul>
						<h4 class="title"><?php echo e(__(@$blog->data_values->title)); ?></h4>
						<p><?php
							echo @$blog->data_values->description
						?></p>
						<ul class="meta-content">
							<li>
								<h5 class="title"><?php echo app('translator')->get('Share On'); ?></h5>
								<ul class="social-icons">
									<li>
										<a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(url()->current())); ?>" class="facebook"><i class="lab la-facebook-f"></i></a>
									</li>
									<li>
										<a href="http://pinterest.com/pin/create/button/?url=<?php echo e(urlencode(url()->current())); ?>&description=<?php echo e(__(@$blog->data_values->short_description)); ?>&media=<?php echo e(frontendImage('blog',@$blog->data_values->image)); ?>" title="<?php echo app('translator')->get('Pinterest'); ?>">

                                            <i class="lab la-pinterest-p"></i>
                                        </a>
									</li>
									<li>
										<a href="http://www.linkedin.com/shareArticle?mini=true&amp;url=<?php echo e(urlencode(url()->current())); ?>&amp;title=my share text&amp;summary=dit is de linkedin summary" title="<?php echo app('translator')->get('Linkedin'); ?>">

                                            <i class="lab la-linkedin-in"></i>
                                        </a>
									</li>
									<li>
										<a href="https://twitter.com/intent/tweet?text=<?php echo e(__(@$blog->data_values->title)); ?>%0A<?php echo e(url()->current()); ?>" title="<?php echo app('translator')->get('Twitter'); ?>">

                                            <i class="lab la-twitter"></i>
                                        </a>
									</li>
								</ul>
							</li>
						</ul>
					</div>
					<div class="fb-comments mt-3" data-href="<?php echo e(route('blog.details',slug($blog->data_values->title))); ?>" data-numposts="5"></div>
				</div>
			</div>
			<div class="col-lg-4 col-md-12">
				<div class="blog-sidebar">
					<div class="sidebar-item">
						<div class="latest-post-wrapper item-inner">
							<h5 class="title"><?php echo app('translator')->get('Latest Post'); ?></h5>
							<?php $__currentLoopData = $latestPost; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $latest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
							<div class="lastest-post-item">
								<div class="thumb">
									<img src="<?php echo e(getImage('assets/images/frontend/blog/thumb_'.$latest->data_values->image)); ?>" alt="blog">
								</div>
								<div class="content">
									<h6 class="title"><a href="<?php echo e(route('blog.details',slug($latest->data_values->title))); ?>"><?php echo e(__(@$latest->data_values->title)); ?></a></h6>
									<ul class="meta-post">
										<li>
											<i class="fas fa-calendar-week"></i> <span> <?php echo e(showDateTime($latest->created_at, 'd M Y')); ?></span>
										</li>
									</ul>
								</div>
							</div>
							<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<?php $__env->stopSection(); ?>


<?php $__env->startPush('fbComment'); ?>
    <?php echo loadExtension('fb-comment') ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate.'layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\blog_details.blade.php ENDPATH**/ ?>