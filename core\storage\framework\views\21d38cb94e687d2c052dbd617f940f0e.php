<?php
    $testContent = getContent('testimonials.content', true);
    $testElements = getContent('testimonials.element', false);
?>

<!-- Section Starts Here -->
<section class="padding-bottom padding-top testimonial-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="section-header text-center">
                    <h2 class="title"><?php echo e(__(@$testContent->data_values->heading)); ?></h2>
                    <p><?php echo e(__(@$testContent->data_values->sub_heading)); ?></p>
                </div>
            </div>
        </div>
        <div class="row justify-content-center gy-5">
            <div class="col-lg-8 col-md-10">
                <div class="testimonial-wrapper">
                    <div class="testimonial-slider">
                        <?php $__currentLoopData = $testElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="single-slider">
                                <div class="testimonial-item">
                                    <div class="content">
                                        <p><?php echo e(__(@$item->data_values->description)); ?></p>
                                    </div>
                                    <div class="thumb-wrapper">
                                        <div class="thumb">
                                            <img src="<?php echo e(frontendImage('testimonials', @$item->data_values->image)); ?>"
                                                alt="testimonials">
                                        </div>
                                        <h5 class="name"><?php echo e(__(@$item->data_values->person)); ?></h5>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<?php if(!app()->offsetExists('slick_script')): ?>
    <?php $__env->startPush('script-lib'); ?>
        <?php $__env->startPush('script-lib'); ?>
            <script src="<?php echo e(asset($activeTemplateTrue . 'js/slick.min.js')); ?>"></script>
        <?php $__env->stopPush(); ?>
    <?php $__env->stopPush(); ?>
    <?php app()->offsetSet('slick_script',true) ?>
<?php endif; ?>

<?php if(!app()->offsetExists('slick_css')): ?>
    <?php $__env->startPush('style-lib'); ?>
        <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'css/slick.css')); ?>">
    <?php $__env->stopPush(); ?>
    <?php app()->offsetSet('slick_css',true) ?>
<?php endif; ?>


<?php $__env->startPush('script'); ?>
    <script>
        $(function() {
            'use strict'

            $(".testimonial-slider").slick({
                fade: false,
                slidesToShow: 1,
                slidesToScroll: 1,
                infinite: true,
                autoplay: false,
                pauseOnHover: true,
                centerMode: true,
                dots: true,
                arrows: false,
                nextArrow: '<i class="las la-arrow-right arrow-right"></i>',
                prevArrow: '<i class="las la-arrow-left arrow-left"></i> ',
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\sections\testimonials.blade.php ENDPATH**/ ?>