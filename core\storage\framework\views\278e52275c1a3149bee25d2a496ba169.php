<?php $__env->startSection('content'); ?>
    <div class="container padding-top padding-bottom">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card custom--card">
                    <div class="card-header">
                        <h5 class="card-title"><?php echo app('translator')->get('Razorpay'); ?></h5>
                    </div>
                    <div class="card-body p-5">
                        <ul class="list-group text-center">
                            <li class="list-group-item d-flex justify-content-between">
                                <?php echo app('translator')->get('You have to pay '); ?>:
                                <strong><?php echo e(showAmount($deposit->final_amount,currencyFormat:false)); ?> <?php echo e(__($deposit->method_currency)); ?></strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <?php echo app('translator')->get('You will get '); ?>:
                                <strong><?php echo e(showAmount($deposit->amount)); ?></strong>
                            </li>
                        </ul>
                         <form action="<?php echo e($data->url); ?>" method="<?php echo e($data->method); ?>">
                            <input type="hidden" custom="<?php echo e($data->custom); ?>" name="hidden">
                            <script src="<?php echo e($data->checkout_js); ?>"
                                    <?php $__currentLoopData = $data->val; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    data-<?php echo e($key); ?>="<?php echo e($value); ?>"
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> >
                            </script>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>


<?php $__env->startPush('script'); ?>
    <script>
        (function ($) {
            "use strict";
            $('input[type="submit"]').addClass("mt-4 btn btn--base w-100");
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate.'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\payment\Razorpay.blade.php ENDPATH**/ ?>