<?php $__env->startSection('content'); ?>
    <div class="padding-top padding-bottom">
        <div class="container">
            <div class="profile__edit__wrapper">
                <div class="profile__edit__form">
                    <form class="register prevent-double-click" action="" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="row justify-content-center">
                            <div class="col-xl-10">
                                <div class="profile__content__edit p-0">
                                    <div class="row gy-3 p-4">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('First Name'); ?></label>
                                                <input type="text" class="form-control form--control radius-0" name="firstname" placeholder="<?php echo app('translator')->get('First Name'); ?>" value="<?php echo e($user->firstname); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('Last Name'); ?></label>
                                                <input type="text" class="form-control form--control radius-0" id="lastname" name="lastname" placeholder="<?php echo app('translator')->get('Last Name'); ?>" value="<?php echo e($user->lastname); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('E-mail Address'); ?></label>
                                                <input class="form-control form--control radius-0" value="<?php echo e($user->email); ?>" disabled>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('Mobile Number'); ?></label>
                                                <input class="form-control form--control radius-0" value="<?php echo e($user->mobile); ?>" disabled>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('Address'); ?></label>
                                                <input type="text" class="form-control form--control radius-0" name="address" placeholder="<?php echo app('translator')->get('Address'); ?>" value="<?php echo e(@$user->address); ?>" required="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('State'); ?></label>
                                                <input type="text" class="form-control form--control radius-0" name="state" placeholder="<?php echo app('translator')->get('state'); ?>" value="<?php echo e(@$user->state); ?>" required="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('Zip Code'); ?></label>
                                                <input type="text" class="form-control form--control radius-0" name="zip" placeholder="<?php echo app('translator')->get('Zip Code'); ?>" value="<?php echo e(@$user->zip); ?>" required="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('City'); ?></label>
                                                <input type="text" class="form-control form--control radius-0" name="city" placeholder="<?php echo app('translator')->get('City'); ?>" value="<?php echo e(@$user->city); ?>" required="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('Zip Code'); ?></label>
                                                <input type="text" class="form-control form--control radius-0" name="zip" placeholder="<?php echo app('translator')->get('Zip Code'); ?>" value="<?php echo e(@$user->zip); ?>" required="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label"><?php echo app('translator')->get('Country'); ?></label>
                                                <input class="form-control form--control radius-0" value="<?php echo e(@$user->country_name); ?>" disabled>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <button type="submit" class="btn btn--base btn--block h-auto"><?php echo app('translator')->get('Update Profile'); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\profile_setting.blade.php ENDPATH**/ ?>