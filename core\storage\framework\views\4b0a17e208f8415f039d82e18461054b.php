<?php
    $hwContent = getContent('how_it_works.content', true);
    $hwClements = getContent('how_it_works.element', false,null, true);
?>
<!-- Working Process Section Starts Here -->
<section class="working-process padding-top padding-bottom">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-10">
                <div class="section-header text-center">
                    <h2 class="title"><?php echo e(__(@$hwContent->data_values->heading)); ?></h2>
                    <p><?php echo e(__(@$hwContent->data_values->sub_heading)); ?></p>
                </div>
            </div>
        </div>
        <div class="row g-4 gy-md-5 justify-content-center">
            <?php $__currentLoopData = $hwClements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6 col-sm-10">
                    <div class="working-process-item">
                        <div class="thumb-wrapper">
                            <span><?php echo e(++$loop->index > 9 ? $loop->index : '0'. $loop->index); ?></span>
                            <div class="thumb">
                            <?php
                                echo @$item->data_values->icon
                            ?>
                        </div>
                        </div>
                        <div class="content">
                            <h4 class="title"><?php echo e(__(@$item->data_values->heading)); ?></h4>
                            <p><?php echo e(__(@$item->data_values->sub_heading)); ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<!-- Working Process Section Ends Here -->
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views/templates/basic/sections/how_it_works.blade.php ENDPATH**/ ?>