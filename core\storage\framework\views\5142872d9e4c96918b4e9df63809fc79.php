<?php $__env->startSection('content'); ?>
    <?php echo $__env->make($activeTemplate . 'partials.social_login', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <form method="POST" class="account-form row" action="<?php echo e(route('user.login')); ?>" onsubmit="return submitUserForm();">
        <?php echo csrf_field(); ?>
        <div class="col-lg-12 form-group form--group">
            <label for="username"><?php echo app('translator')->get('Username'); ?></label>
            <input id="username" name="username" type="text" class="form--control" placeholder="<?php echo app('translator')->get('Enter Your username'); ?>" required>
        </div>
        <div class="col-lg-12 form-group form--group">
            <label for="password"><?php echo app('translator')->get('Password'); ?></label>
            <input id="password" type="password" name="password" class="form--control" placeholder="<?php echo app('translator')->get('Enter Your Password'); ?>" required>
        </div>
        <div class="col-lg-12">
            <?php if (isset($component)) { $__componentOriginalff0a9fdc5428085522b49c68070c11d6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalff0a9fdc5428085522b49c68070c11d6 = $attributes; } ?>
<?php $component = App\View\Components\Captcha::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('captcha'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Captcha::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalff0a9fdc5428085522b49c68070c11d6)): ?>
<?php $attributes = $__attributesOriginalff0a9fdc5428085522b49c68070c11d6; ?>
<?php unset($__attributesOriginalff0a9fdc5428085522b49c68070c11d6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalff0a9fdc5428085522b49c68070c11d6)): ?>
<?php $component = $__componentOriginalff0a9fdc5428085522b49c68070c11d6; ?>
<?php unset($__componentOriginalff0a9fdc5428085522b49c68070c11d6); ?>
<?php endif; ?>
        </div>

        <div class="col-lg-12 d-flex justify-content-between">
            <div class="form-group form--group custom--checkbox">
                <input type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                <label for="remember"><?php echo app('translator')->get('Remember Me'); ?></label>
            </div>
            <div class="">
                <a href="<?php echo e(route('user.password.request')); ?>"><?php echo app('translator')->get('Forgot Password?'); ?></a>
            </div>
        </div>
        <div class="col-md-12 form-group form--group">
            <button class="account-button w-100" type="submit"><?php echo app('translator')->get('Sign In'); ?></button>
        </div>
        <div class="col-md-12">
            <div class="account-page-link">
                <p><?php echo app('translator')->get('Don\'t have any Account?'); ?> <a href="https://book.superiortour.com.sg/account/register" target="_blank"><?php echo app('translator')->get('Sign Up'); ?></a></p>
            </div>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        "use strict";

        function submitUserForm() {
            var response = grecaptcha.getResponse();
            if (response.length == 0) {
                document.getElementById('g-recaptcha-error').innerHTML =
                    '<span class="text-danger"><?php echo app('translator')->get('Captcha field is required.'); ?></span>';
                return false;
            }
            return true;
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.authenticate', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\auth\login.blade.php ENDPATH**/ ?>