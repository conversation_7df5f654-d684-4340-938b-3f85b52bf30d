<?php $__env->startSection('panel'); ?>
    <?php
        $searchSettings = getContent('search_form.settings', true);
        $settings = $searchSettings ? $searchSettings->data_values : null;
    ?>
    
    <div class="row mb-none-30">
        <div class="col-lg-12 col-md-12 mb-30">
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="alert alert-info">
                                    <i class="las la-info-circle"></i>
                                    <strong><?php echo app('translator')->get('Search Form Redirection Settings'); ?></strong><br>
                                    <?php echo app('translator')->get('Configure how the search form redirects to external booking systems. Set the target domain and parameter names that will be used in the URL.'); ?>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('Redirect Domain'); ?></label>
                                    <input class="form-control" type="url" name="search_redirect_domain" 
                                           required value="<?php echo e(@$settings->redirect_domain); ?>" 
                                           placeholder="https://example.com">
                                    <small class="text-muted"><?php echo app('translator')->get('The domain where search results will be redirected (e.g., https://booking.example.com)'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('From Location Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_from_param" 
                                           required value="<?php echo e(@$settings->from_param ?? 'from'); ?>" 
                                           placeholder="from">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for departure location'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('To Location Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_to_param" 
                                           required value="<?php echo e(@$settings->to_param ?? 'to'); ?>" 
                                           placeholder="to">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for destination location'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('Departure Date Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_departure_param" 
                                           required value="<?php echo e(@$settings->departure_param ?? 'departure'); ?>" 
                                           placeholder="departure">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for departure date'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Return Date Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_return_param" 
                                           value="<?php echo e(@$settings->return_param ?? 'return'); ?>" 
                                           placeholder="return">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for return date (optional for round trips)'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Passengers Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_passengers_param"
                                           value="<?php echo e(@$settings->passengers_param ?? 'passengers'); ?>"
                                           placeholder="passengers">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for number of passengers (optional)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Trip Type Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_triptype_param"
                                           value="<?php echo e(@$settings->triptype_param ?? 'TripType'); ?>"
                                           placeholder="TripType">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for trip type (1=One Way, 2=Round Trip)'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0"><?php echo app('translator')->get('Example URL Structure'); ?></h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2"><strong><?php echo app('translator')->get('Generated URL will look like:'); ?></strong></p>
                                        <code id="example-url">
                                            <?php echo e(@$settings->redirect_domain ?? 'https://example.com'); ?>?<?php echo e(@$settings->triptype_param ?? 'TripType'); ?>=2&<?php echo e(@$settings->from_param ?? 'from'); ?>=Singapore&<?php echo e(@$settings->to_param ?? 'to'); ?>=Kuala%20Lumpur&<?php echo e(@$settings->departure_param ?? 'departure'); ?>=2024-01-15&<?php echo e(@$settings->return_param ?? 'return'); ?>=2024-01-20&<?php echo e(@$settings->passengers_param ?? 'passengers'); ?>=2
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn--primary w-100 h-45"><?php echo app('translator')->get('Submit'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
<script>
    'use strict';
    
    // Update example URL when inputs change
    function updateExampleUrl() {
        const domain = $('input[name="search_redirect_domain"]').val() || 'https://example.com';
        const fromParam = $('input[name="search_from_param"]').val() || 'from';
        const toParam = $('input[name="search_to_param"]').val() || 'to';
        const departureParam = $('input[name="search_departure_param"]').val() || 'departure';
        const returnParam = $('input[name="search_return_param"]').val() || 'return';
        const passengersParam = $('input[name="search_passengers_param"]').val() || 'passengers';
        const triptypeParam = $('input[name="search_triptype_param"]').val() || 'TripType';

        const exampleUrl = `${domain}?${triptypeParam}=2&${fromParam}=Singapore&${toParam}=Kuala%20Lumpur&${departureParam}=2024-01-15&${returnParam}=2024-01-20&${passengersParam}=2`;

        $('#example-url').text(exampleUrl);
    }
    
    // Update example URL on input change
    $('input[name^="search_"]').on('input', updateExampleUrl);
    
    // Initial update
    updateExampleUrl();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\setting\search_form.blade.php ENDPATH**/ ?>