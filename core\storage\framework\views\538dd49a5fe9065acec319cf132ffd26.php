<?php $__env->startSection('panel'); ?>
    <div class="row mb-none-30">
        <div class="col-xl-12 col-lg-12 col-md-12 mb-30">
            <div class="card">
                <h5 class="card-header"><?php echo app('translator')->get('Information About Ticket Price'); ?> </h5>
                <div class="card-body">

                    <form action="<?php echo e(route('admin.trip.ticket.price.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label> <?php echo app('translator')->get('Fleet Type'); ?></label>
                                    <select name="fleet_type" class="select2" required>
                                        <option value=""><?php echo app('translator')->get('Select an option'); ?></option>
                                        <?php $__currentLoopData = $fleetTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item->id); ?>"><?php echo e(__($item->name)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label> <?php echo app('translator')->get('Route'); ?></label>
                                    <select name="route" class="select2" required>
                                        <option value=""><?php echo app('translator')->get('Select an option'); ?></option>
                                        <?php $__currentLoopData = $routes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item->id); ?>"><?php echo e(__($item->name)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label> <?php echo app('translator')->get('Price For Source To Destination'); ?></label>
                                    <div class="input-group">
                                        <div class="input-group-text"><?php echo e(__(gs('cur_text'))); ?></div>
                                        <input type="number" step="any" class="form-control" name="main_price" required value="<?php echo e(old('main_price')); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 price-error-message"></div>

                            <div class="price-wrapper col-md-12"></div>
                        </div>
                        <button type="submit" class="btn btn--primary h-45 w-100 submit-button"><?php echo app('translator')->get('Submit'); ?></button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('breadcrumb-plugins'); ?>
    <a href="<?php echo e(route('admin.trip.ticket.price.index')); ?>" class="btn btn-sm btn-outline--primary "><i class="la la-fw la-backward"></i><?php echo app('translator')->get('Go Back'); ?></a>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        "use strict";

        (function($) {
            $(document).on('change', 'select[name=fleet_type] , select[name=route]', function() {
                var routeId = $('select[name="route"]').find("option:selected").val();
                var fleetTypeId = $('select[name="fleet_type"]').find("option:selected").val();

                if (routeId && fleetTypeId) {
                    var data = {
                        'vehicle_route_id': routeId,
                        'fleet_type_id': fleetTypeId
                    }
                    $.ajax({
                        url: "<?php echo e(route('admin.trip.ticket.get_route_data')); ?>",
                        method: "get",
                        data: data,
                        success: function(result) {
                            if (result.error) {
                                $('.price-error-message').html(
                                    `<h5 class="text--danger">${result.error}</h5>`);
                                $('.price-wrapper').html('');
                                $('.submit-button').attr('disabled', 'disabled');
                            } else {
                                $('.price-error-message').html(``);
                                $('.submit-button').removeAttr('disabled');
                                $('.price-wrapper').html(`<h5>${result}</h5>`);
                            }
                        }
                    });
                } else {
                    $('.price-wrapper').html('');
                }
            })
        })(jQuery)
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\trip\ticket\add_price.blade.php ENDPATH**/ ?>