<?php $__env->startSection('content'); ?>
    <div class="verification-code-wrapper">
        <div class="verification-area">
            <form action="<?php echo e(route('user.password.verify.code')); ?>" method="POST" class="submit-form">
                <?php echo csrf_field(); ?>
                <p class="verification-text"><?php echo app('translator')->get('A 6 digit verification code sent to your email address'); ?> : <?php echo e(showEmailAddress($email)); ?></p>
                <input type="hidden" name="email" value="<?php echo e($email); ?>">
                <?php echo $__env->make($activeTemplate . 'partials.verification_code', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <div class="form-group">
                    <button type="submit" class="btn btn--base w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
                <div class="form-group">
                    <?php echo app('translator')->get('Please check including your Junk/Spam Folder. if not found, you can'); ?>
                    <a href="<?php echo e(route('user.password.request')); ?>"><?php echo app('translator')->get('Try to send again'); ?></a>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.authenticate', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\auth\passwords\code_verify.blade.php ENDPATH**/ ?>