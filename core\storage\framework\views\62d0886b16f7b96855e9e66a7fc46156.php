<?php $__env->startSection('content'); ?>

    <div class="container padding-top padding-bottom">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card cmn--card">
                    <div class="card-header d-flex flex-wrap justify-content-between align-items-center" style="gap:10px">
                        <h5 class="card-title m-0 text-white">
                            <?php echo $myTicket->statusBadge ?>
                            [<?php echo app('translator')->get('Ticket'); ?>#<?php echo e($myTicket->ticket); ?>] <?php echo e($myTicket->subject); ?>

                        </h5>

                        <?php if($myTicket->status != Status::TICKET_CLOSE && $myTicket->user): ?>
                            <button class="btn btn--danger w-auto h-auto confirmationBtn" type="button" data-action="<?php echo e(route('ticket.close', $myTicket->id)); ?>" data-question="<?php echo app('translator')->get('Are you sure to close this ticket?'); ?>"><i class="fa fa-lg fa-times-circle"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="accordionExample">
                            <div id="collapseThree" class="collapse show" aria-labelledby="headingThree" data-parent="#accordionExample">
                                <?php if($myTicket->status != 4): ?>
                                    <form method="post" action="<?php echo e(route('ticket.reply', $myTicket->id)); ?>" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="replayTicket" value="1">
                                        <div class="row gy-3 justify-content-between">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <textarea name="message" class="form-control form--control shadow-none" id="inputMessage" placeholder="<?php echo app('translator')->get('Your Reply'); ?>" rows="4" cols="10"></textarea>
                                                </div>
                                            </div>

                                            <div class="col-md-9">
                                                <button type="button" class="btn btn-dark btn-sm addAttachment ">
                                                    <i class="fas fa-plus"></i> <?php echo app('translator')->get('Add Attachment'); ?> </button>
                                                <p class="my-2"><span class="text--info"><?php echo app('translator')->get('Max 5 files can be uploaded | Maximum upload size is ' . convertToReadableSize(ini_get('upload_max_filesize')) . ' | Allowed File Extensions: .jpg, .jpeg, .png, .pdf, .doc, .docx'); ?></span>
                                                </p>
                                                <div class="row g-3 fileUploadsContainer"></div>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="submit" class="btn btn--base my-2 w-100 h-40">
                                                    <i class="fa fa-reply"></i> <?php echo app('translator')->get('Reply'); ?>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row pt-3">
            <div class="col-md-12">
                <div class="card cmn--card">
                    <div class="card-body">
                        <?php $__currentLoopData = $messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($message->admin_id == 0): ?>
                                <div class="row border border-primary border-radius-3 my-sm-3 my-2 py-3 mx-0 mx-sm-2" style="background-color: #dbe9ff">
                                    <div class="col-md-3 border--right text-right">
                                        <h5 class="my-3"><?php echo e($message->ticket->name); ?></h5>
                                    </div>
                                    <div class="col-md-9 ps-2">
                                        <p class="text-muted fw-bold">
                                            <?php echo app('translator')->get('Posted on'); ?>
                                            <?php echo e($message->created_at->format('l, dS F Y @ H:i')); ?></p>
                                        <p><?php echo e($message->message); ?></p>
                                        <?php if($message->attachments()->count() > 0): ?>
                                            <div class="mt-2">
                                                <?php $__currentLoopData = $message->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(route('ticket.download', encrypt($image->id))); ?>" class="text--base"><i class="fa fa-file"></i>
                                                        <?php echo app('translator')->get('Attachment'); ?> <?php echo e(++$k); ?> </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="row border border-warning border-radius-3 my-sm-3 my-2 py-3 mx-0 mx-sm-2">
                                    <div class="col-md-3 border--right text-right">
                                        <h5 class="my-1"><?php echo e($message->admin->name); ?></h5>
                                        <p class="lead text-muted"><?php echo app('translator')->get('Staff'); ?></p>
                                    </div>
                                    <div class="col-md-9">
                                        <p class="text-muted fw-bold">
                                            <?php echo app('translator')->get('Posted on'); ?>
                                            <?php echo e($message->created_at->format('l, dS F Y @ H:i')); ?></p>
                                        <p><?php echo e($message->message); ?></p>
                                        <?php if($message->attachments()->count() > 0): ?>
                                            <div class="mt-2">
                                                <?php $__currentLoopData = $message->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(route('ticket.download', encrypt($image->id))); ?>" class="text--base"><i class="fa fa-file"></i>
                                                        <?php echo app('translator')->get('Attachment'); ?> <?php echo e(++$k); ?> </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\ConfirmationModal::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        .cmn--card button[type="submit"] {
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        @media (max-width: 374px) {
            .cmn--card button[type="submit"] {
                width: 100%;
            }
        }

        .fileUploadsContainer .form--control[type="file"] {
            padding: 0;
            position: relative;
        }

        .fileUploadsContainer .form--control[type="file"]::file-selector-button {
            border: none;
            padding: 4px 10px;
            border-radius: 0em;
            color: #fff;
            background-color: var(--main-color) !important;
            transition: 0.2s linear;
            line-height: 30px;
            position: relative;
            margin-left: 0px;
        }

        .fileUploadsContainer .input-group {
            flex-wrap: nowrap
        }

        .fileUploadsContainer .input-group-text {
            height: auto;
            color: #fff;
        }

        #confirmationModal .modal-header .close {
            padding: 0;
            background: transparent;
            color: #000;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            var fileAdded = 0;
            $('.addAttachment').on('click', function() {
                fileAdded++;
                if (fileAdded == 5) {
                    $(this).attr('disabled', true)
                }
                $(".fileUploadsContainer").append(`
                    <div class="col-lg-4 col-md-12 removeFileInput">
                        <div class="input-group">
                            <input type="file" name="attachments[]" class="form--control" accept=".jpeg,.jpg,.png,.pdf,.doc,.docx" required>
                            <button type="button" class="input-group-text removeFile bg--danger border-0"><i class="fas fa-times"></i></button>
                        </div>
                    </div>
                `)
            });
            $(document).on('click', '.removeFile', function() {
                $('.addAttachment').removeAttr('disabled', true)
                fileAdded--;
                $(this).closest('.removeFileInput').remove();
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . $layout, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\support\view.blade.php ENDPATH**/ ?>