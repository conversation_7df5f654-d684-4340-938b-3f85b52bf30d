<?php $__env->startSection('content'); ?>

    <?php if($sections != null): ?>
        <?php
            $decodedSections = is_string($sections) ? json_decode($sections) : $sections;
            $decodedSections = is_array($decodedSections) || is_object($decodedSections) ? $decodedSections : [];
        ?>
        <?php $__currentLoopData = $decodedSections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make($activeTemplate.'sections.'.$sec, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate.'layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\pages.blade.php ENDPATH**/ ?>