<?php $__env->startSection('content'); ?>
    <div class="padding-top padding-bottom section-bg">
        <div class="container">
            <div class="row justify-content-center gy-4 gy-sm-5">
                <div class="col-md-12">
                    <div class="d-flex justify-content-end mb-4">
                        <a href="<?php echo e(route('ticket.open')); ?>" class="btn btn--base btn-sm">
                            <i class="fas fa-plus"></i>
                            <?php echo app('translator')->get('New Ticket'); ?>
                        </a>
                    </div>

                    <div
                        class="table-responsive table-responsive-xl table-responsive-lg table-responsive-md table-responsive-sm">
                        <table class="booking-table">
                            <thead class="thead">
                                <tr>
                                    <th><?php echo app('translator')->get('Subject'); ?></th>
                                    <th><?php echo app('translator')->get('Status'); ?></th>
                                    <th><?php echo app('translator')->get('Priority'); ?></th>
                                    <th><?php echo app('translator')->get('Last Reply'); ?></th>
                                    <th><?php echo app('translator')->get('Action'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $supports; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $support): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td data-label="<?php echo app('translator')->get('Subject'); ?>"> <a
                                                href="<?php echo e(route('ticket.view', $support->ticket)); ?>"
                                                class="font-weight-bold"> [<?php echo app('translator')->get('Ticket'); ?>#<?php echo e($support->ticket); ?>]
                                                <?php echo e(__($support->subject)); ?> </a></td>
                                        <td data-label="<?php echo app('translator')->get('Status'); ?>">
                                            <?php if($support->status == 0): ?>
                                                <span class="badge badge--success py-2 px-3"><?php echo app('translator')->get('Open'); ?></span>
                                            <?php elseif($support->status == 1): ?>
                                                <span class="badge badge--primary py-2 px-3"><?php echo app('translator')->get('Answered'); ?></span>
                                            <?php elseif($support->status == 2): ?>
                                                <span class="badge badge--warning py-2 px-3"><?php echo app('translator')->get('Customer Reply'); ?></span>
                                            <?php elseif($support->status == 3): ?>
                                                <span class="badge badge--dark py-2 px-3"><?php echo app('translator')->get('Closed'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Priority'); ?>">
                                            <?php if($support->priority == 1): ?>
                                                <span class="badge badge--dark py-2 px-3"><?php echo app('translator')->get('Low'); ?></span>
                                            <?php elseif($support->priority == 2): ?>
                                                <span class="badge badge--success py-2 px-3"><?php echo app('translator')->get('Medium'); ?></span>
                                            <?php elseif($support->priority == 3): ?>
                                                <span class="badge badge--primary py-2 px-3"><?php echo app('translator')->get('High'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Last Reply'); ?>">
                                            <?php echo e(\Carbon\Carbon::parse($support->last_reply)->diffForHumans()); ?> </td>

                                        <td data-label="<?php echo app('translator')->get('Action'); ?>">
                                            <a href="<?php echo e(route('ticket.view', $support->ticket)); ?>"
                                                class="btn btn--base btn-sm">
                                                <i class="fa fa-desktop"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <?php echo e(__($emptyMessage)); ?>

                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if($supports->hasPages()): ?>
                        <?php echo e($supports->links()); ?>

                    <?php endif; ?>
                </div>
            </div>
            <div class="col-12">

            </div>
        </div>
    </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\support\index.blade.php ENDPATH**/ ?>