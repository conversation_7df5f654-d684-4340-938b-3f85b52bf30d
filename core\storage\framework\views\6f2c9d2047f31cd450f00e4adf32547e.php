<?php
    $content = getContent('contact.content', true);
    $language = App\Models\Language::all();
    $selectedLang = $language->where('code', session('lang'))->first();
?>
<!-- Header Section Starts Here -->
<div class="header-top">
    <div class="container">
        <div class="header-top-area">
            <ul class="left-content">
                <li>
                    <i class="las la-phone"></i>
                    <a href="tel:<?php echo e(__(@$content->data_values->contact_number)); ?>">
                        <?php echo e(__(@$content->data_values->contact_number)); ?>

                    </a>
                </li>
                <li>
                    <i class="las la-envelope-open"></i>
                    <a href="mailto:<?php echo e(__(@$content->data_values->email)); ?>">
                        <?php echo e(__(@$content->data_values->email)); ?>

                    </a>
                </li>
            </ul>
            <div class="right-content">
                <div>
                    <?php if(gs('multi_language')): ?>
                        <div>
                            <div class="language dropdown">
                                <button class="language-wrapper" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="language-content">
                                        <div class="language_flag">
                                            <img src="<?php echo e(getImage(getFilePath('language') . '/' . @$selectedLang->image, getFileSize('language'))); ?>" alt="flag">
                                        </div>
                                        <p class="language_text_select"><?php echo e(__(@$selectedLang->name)); ?></p>
                                    </div>
                                    <span class="collapse-icon"><i class="las la-angle-down"></i></span>
                                </button>
                                <div class="dropdown-menu langList_dropdow py-2">
                                    <ul class="langList">
                                        <?php $__currentLoopData = $language; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="language-list langSel" data-code="<?php echo e($item->code); ?>">
                                                <div class="language_flag">
                                                    <img src="<?php echo e(getImage(getFilePath('language') . '/' . $item->image, getFileSize('language'))); ?>" alt="flag">
                                                </div>
                                                <p class="language_text"><?php echo e($item->name); ?></p>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="header-bottom">
    <div class="container">
        <div class="header-bottom-area">
            <div class="logo">
                <a href="<?php echo e(route('home')); ?>">
                    <img src="<?php echo e(siteLogo()); ?>" alt="<?php echo app('translator')->get('Logo'); ?>">
                </a>
            </div> <!-- Logo End -->
            <ul class="menu">
                <li>
                    <a href="<?php echo e(route('user.home')); ?>"><?php echo app('translator')->get('Dashboard'); ?></a>
                </li>
                <li>
                    <a href="javascript::void()"><?php echo app('translator')->get('Booking'); ?></a>
                    <ul class="sub-menu">
                        <li>
                            <a href="<?php echo e(route('ticket')); ?>"><?php echo app('translator')->get('Buy Ticket'); ?></a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('user.ticket.history')); ?>"><?php echo app('translator')->get('Booking History'); ?></a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript::void()"><?php echo app('translator')->get('Support Ticket'); ?></a>
                    <ul class="sub-menu">
                        <li>
                            <a href="<?php echo e(route('ticket.open')); ?>"><?php echo app('translator')->get('Create New'); ?></a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('ticket.index')); ?>"><?php echo app('translator')->get('Tickets'); ?></a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#0"><?php echo app('translator')->get('Profile'); ?></a>
                    <ul class="sub-menu">
                        <li>
                            <a href="<?php echo e(route('user.profile.setting')); ?>"><?php echo app('translator')->get('Profile'); ?></a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('user.change.password')); ?>"><?php echo app('translator')->get('Change Password'); ?></a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('user.logout')); ?>"><?php echo app('translator')->get('Logout'); ?></a>
                        </li>
                    </ul>
                </li>
            </ul>
            <div class="d-flex flex-wrap algin-items-center">
                <a href="<?php echo e(route('ticket')); ?>" class="cmn--btn btn--sm"><?php echo app('translator')->get('Buy Tickets'); ?></a>
                <div class="header-trigger-wrapper d-flex d-lg-none ms-4">
                    <div class="header-trigger d-block d-lg-none">
                        <span></span>
                    </div>
                    <div class="top-bar-trigger">
                        <i class="las la-ellipsis-v"></i>
                    </div>
                </div><!-- Trigger End-->
            </div>
        </div>
    </div>
</div>
<!-- Header Section Ends Here -->

<?php $__env->startPush('style'); ?>
    <style>
        .language-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
            width: max-content;
            margin-left: 12px;
            padding: 0;
            background-color: transparent;
            border: 0;
        }

        .language_flag {
            flex-shrink: 0;
            display: flex;
        }

        .language_flag img {
            height: 20px;
            width: 20px;
            object-fit: cover;
            border-radius: 50%;
        }

        .language-wrapper.show .collapse-icon {
            transform: rotate(180deg)
        }

        .collapse-icon {
            font-size: 14px;
            display: flex;
            transition: all linear 0.2s;
            color: #111
        }

        .language_text_select {
            font-size: 14px;
            font-weight: 400;
            color: #111;
        }

        .language-content {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .language_text {
            color: #111
        }

        .language-list {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            cursor: pointer;
        }

        .language-list:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .language .dropdown-menu {
            position: absolute;
            opacity: 0;
            visibility: hidden;
            top: 100%;
            display: unset;
            background: #ffffffea;
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.08);
            min-width: 150px;
            padding: 7px 0 !important;
            border-radius: 8px;
            border: 1px solid rgb(255 255 255 / 10%);
        }

        .language .dropdown-menu.show {
            visibility: visible;
            opacity: 1;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        $(document).ready(function() {
            "use strict";
            $(".langSel").on("click", function() {
                window.location.href = "<?php echo e(route('home')); ?>/change/" + $(this).data('code');
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\partials\user_header.blade.php ENDPATH**/ ?>