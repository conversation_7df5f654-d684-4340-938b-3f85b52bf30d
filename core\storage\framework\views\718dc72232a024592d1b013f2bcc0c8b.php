<?php $__env->startSection('content'); ?>
    <?php
        try {
            $pickupLocations = App\Models\Location::pickup()->active()->get();
            $dropLocations = App\Models\Location::drop()->active()->get();
        } catch (Exception $e) {
            $pickupLocations = collect(); // Empty collection if query fails
            $dropLocations = collect(); // Empty collection if query fails
        }
    ?>

    <div class="ticket-search-bar bg_img padding-top" style="background: url(<?php echo e(getImage('assets/templates/basic/images/search_bg.jpg')); ?>) left center;">
        <div class="container">
            <div class="bus-search-header">
                <form id="ticketBookingForm" class="ticket-form ticket-form-two row g-3 justify-content-center">
                    <div class="col-md-4 col-lg-3">
                        <div class="form--group">
                            <i class="las la-location-arrow"></i>
                            <select name="pickup" id="ticketPickupLocation" class="form--control select2" data-placeholder="<?php echo app('translator')->get('Pickup Point'); ?>">
                                <option value=""><?php echo app('translator')->get('Pickup Point'); ?></option>
                                <?php $__currentLoopData = $pickupLocations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($location->id); ?>" data-slug="<?php echo e($location->slug); ?>"
                                        <?php if(request()->pickup == $location->id): ?> selected <?php endif; ?>>
                                        <?php echo e(__($location->name)); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form--group">
                            <i class="las la-map-marker"></i>
                            <select name="destination" id="ticketDropLocation" class="form--control select2" data-placeholder="<?php echo app('translator')->get('Dropping Point'); ?>">
                                <option value=""><?php echo app('translator')->get('Dropping Point'); ?></option>
                                <?php $__currentLoopData = $dropLocations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($location->id); ?>" data-slug="<?php echo e($location->slug); ?>"
                                        <?php if(request()->destination == $location->id): ?> selected <?php endif; ?>>
                                        <?php echo e(__($location->name)); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form--group">
                            <i class="las la-calendar-check"></i>
                            <input type="text" name="date_of_journey" class="form--control date-range" placeholder="<?php echo app('translator')->get('Date of Journey'); ?>" autocomplete="off" value="<?php echo e(request()->date_of_journey); ?>">
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="form--group">
                            <button class="btn btn--base w-100"><?php echo app('translator')->get('Find Tickets'); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Ticket Search Starts -->

    <!-- Ticket Section Starts Here -->
    <section class="ticket-section padding-bottom section-bg">
        <div class="container">
            <div class="row gy-5">
                <div class="col-lg-3">
                    <form action="<?php echo e(route('search')); ?>" id="filterForm">
                        <div class="ticket-filter">
                            <div class="filter-header filter-item">
                                <h4 class="title mb-0"><?php echo app('translator')->get('Filter'); ?></h4>
                                <button type="reset" class="reset-button h-auto"><?php echo app('translator')->get('Reset All'); ?></button>
                            </div>
                            <?php if($fleetType): ?>
                                <div class="filter-item">
                                    <h5 class="title"><?php echo app('translator')->get('Vehicle Type'); ?></h5>
                                    <ul class="bus-type">
                                        <?php $__currentLoopData = $fleetType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fleet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="custom--checkbox">
                                                <input name="fleetType[]" class="search" value="<?php echo e($fleet->id); ?>" id="<?php echo e($fleet->name); ?>" type="checkbox" <?php if(request()->fleetType): ?> <?php $__currentLoopData = request()->fleetType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($item == $fleet->id): ?>
                                    checked <?php endif; ?> <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?> >
                                        <label for="<?php echo e($fleet->name); ?>"><span><i class="las la-bus"></i><?php echo e(__($fleet->name)); ?></span></label>
                                        </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <?php if($routes): ?>
                            <div class="filter-item">
                                <h5 class="title"><?php echo app('translator')->get('Routes'); ?></h5>
                                <ul class="bus-type">
                                    <?php $__currentLoopData = $routes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $route): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="custom--checkbox">
                                            <input name="routes[]" class="search" value="<?php echo e($route->id); ?>" id="route.<?php echo e($route->id); ?>" type="checkbox" <?php if(request()->routes): ?> <?php $__currentLoopData = request()->routes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($item == $route->id): ?> checked <?php endif; ?> <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?> >
                                    <label for="route.<?php echo e($route->id); ?>"><span><span><i class="las la-road"></i><?php echo e(__($route->name)); ?> </span></label>
                                    </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                </div>
                <?php endif; ?>

                <?php if($schedules): ?>
                    <div class="filter-item">
                        <h5 class="title"><?php echo app('translator')->get('Schedules'); ?></h5>
                        <ul class="bus-type">
                            <?php $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="custom--checkbox">
                                    <input name="schedules[]" class="search" value="<?php echo e($schedule->id); ?>" id="schedule.<?php echo e($schedule->id); ?>" type="checkbox" <?php if(request()->schedules): ?> <?php $__currentLoopData = request()->schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($item == $schedule->id): ?>
                                    checked <?php endif; ?> <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>>
                            <label for="schedule.<?php echo e($schedule->id); ?>"><span><span><i class="las la-clock"></i>
                                        <?php echo e(showDateTime($schedule->start_from, 'h:i a') . ' - ' . showDateTime($schedule->end_at, 'h:i a')); ?>

                                    </span></label>
                            </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
        </form>
        </div>

        <div class="col-lg-9">
            <div class="ticket-wrapper">
                <?php $__empty_1 = true; $__currentLoopData = $trips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $trip): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <?php
                        $start = Carbon\Carbon::parse($trip->schedule->start_from);
                        $end = Carbon\Carbon::parse($trip->schedule->end_at);

                        if ($end->lt($start)) {
                            $end->addDay();
                        }
                        $diff = $start->diff($end);

                        $ticket = App\Models\TicketPrice::where('fleet_type_id', $trip->fleetType->id)
                            ->where('vehicle_route_id', $trip->route->id)
                            ->first();
                    ?>

                    <?php if($ticket): ?>
                        <div class="ticket-item">
                            <div class="ticket-item-inner">
                                <h5 class="bus-name"><?php echo e(__($trip->title)); ?></h5>
                                <span class="bus-info"><?php echo app('translator')->get('Seat Layout - '); ?> <?php echo e(__($trip->fleetType->seat_layout)); ?></span>
                                <span class="ratting"><i class="las la-bus"></i><?php echo e(__($trip->fleetType->name)); ?></span>
                            </div>
                            <div class="ticket-item-inner travel-time">
                                <div class="bus-time">
                                    <p class="time"><?php echo e(showDateTime($trip->schedule->start_from, 'h:i A')); ?></p>
                                    <p class="place"><?php echo e(__($trip->startFrom->name)); ?></p>
                                </div>
                                <div class=" bus-time">
                                    <i class="las la-arrow-right"></i>
                                    <p><?php echo e($diff->format('%H:%I min')); ?></p>
                                </div>
                                <div class=" bus-time">
                                    <p class="time"><?php echo e(showDateTime($trip->schedule->end_at, 'h:i A')); ?></p>
                                    <p class="place"><?php echo e(__($trip->endTo->name)); ?></p>
                                </div>
                            </div>
                            <div class="ticket-item-inner book-ticket">
                                <p class="rent mb-0">
                                    <?php echo e(__(gs('cur_sym'))); ?><?php echo e(showAmount($ticket->price, currencyFormat: false)); ?></p>
                                <?php if($trip->day_off): ?>
                                    <div class="seats-left mt-2 mb-3 fs--14px">
                                        <?php echo app('translator')->get('Off Days'); ?>: <div class="d-inline-flex flex-wrap" style="gap:5px">
                                            <?php $__currentLoopData = $trip->day_off; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge badge--primary"><?php echo e(__(showDayOff($item))); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php else: ?>
                                        <?php echo app('translator')->get('Every day available'); ?>
                                <?php endif; ?>
                            </div>
                            <a class="btn btn--base" href="<?php echo e(route('ticket.seats', [$trip->id, slug($trip->title)])); ?>"><?php echo app('translator')->get('Select Seat'); ?></a>
                        </div>
                        <?php if($trip->fleetType->facilities): ?>
                            <div class="ticket-item-footer">
                                <div class="d-flex content-justify-center">
                                    <span>
                                        <strong><?php echo app('translator')->get('Facilities - '); ?></strong>
                                        <?php $__currentLoopData = $trip->fleetType->facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="facilities"><?php echo e(__($item)); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
            </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="ticket-item">
                <h5><?php echo e(__($emptyMessage)); ?></h5>
            </div>
            <?php endif; ?>
            <?php if($trips->hasPages()): ?>
                <div class="custom-pagination">
                    <?php echo e(paginateLinks($trips)); ?>

                </div>
            <?php endif; ?>
        </div>
        </div>
        </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style-lib'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/global/css/select2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/global/css/daterangepicker.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-lib'); ?>
    <script src="<?php echo e(asset('assets/global/js/select2.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/global/js/moment.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/global/js/daterangepicker.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            $('.search').on('change', function() {
                $('#filterForm').submit();
            });

            $('.select2').select2({
                minimumResultsForSearch: Infinity,
                placeholder: function() {
                    return $(this).data('placeholder') || this.getAttribute('placeholder') || 'Select an option';
                },
                allowClear: false
            });

            const datePicker = $('.date-range').daterangepicker({
                autoUpdateInput: true,
                singleDatePicker: true,
                minDate: new Date(),
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            // Form submission handler for ticket page
            $('#ticketBookingForm').on('submit', function(e) {
                e.preventDefault();

                // Get form values
                const pickupId = $('#ticketPickupLocation').val();
                const dropId = $('#ticketDropLocation').val();
                const departDate = $('input[name="date_of_journey"]').val();
                const tripType = $('input[name="trip_type"]:checked').val() || 'one_way';

                // Validation
                if (!pickupId) {
                    alert('Please select a pickup location');
                    return;
                }
                if (!dropId) {
                    alert('Please select a drop location');
                    return;
                }
                if (!departDate) {
                    alert('Please select a departure date');
                    return;
                }

                // Get location slugs
                const pickupSlug = $('#ticketPickupLocation option:selected').data('slug');
                const dropSlug = $('#ticketDropLocation option:selected').data('slug');

                // Build route slug (pickup-to-drop)
                const routeSlug = pickupSlug + '-to-' + dropSlug;

                // Format dates (YYYY-MM-DD)
                const formattedDepartDate = moment(departDate).format('YYYY-MM-DD');
                let bookingUrl = `https://book.superiortour.com.sg/bus/booking/${routeSlug}?TripType=1&departdate=${formattedDepartDate}`;

                // Debug: Log the URL
                console.log('Generated booking URL:', bookingUrl);

                // Redirect to booking system
                window.open(bookingUrl, '_blank');
            });


            $('.reset-button').on('click', function() {
                $('.search').attr('checked', false);
                $('#filterForm').submit();
            })
        })(jQuery)
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . $layout, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\ticket.blade.php ENDPATH**/ ?>