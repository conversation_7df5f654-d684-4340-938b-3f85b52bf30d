<?php
    $amenitiesContent = getContent('amenities.content', true);
    $facilities = getContent('amenities.element', false, null, true);
?>
<!-- Our Ameninies Section Starts Here -->
<section class="amenities-section padding-bottom">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-10">
                <div class="section-header text-center">
                    <h2 class="title"><?php echo e(__(@$amenitiesContent->data_values->heading)); ?></h2>
                    <p><?php echo e(__(@$amenitiesContent->data_values->sub_heading)); ?></p>
                </div>
            </div>
        </div>
        <div class="amenities-wrapper">
            <div class="amenities-slider">
                <?php $__currentLoopData = $facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $data = is_string($item->data_values) ? json_decode($item->data_values) : $item->data_values;
                    ?>
                    <div class="single-slider">
                        <div class="amenities-item">
                            <div class="thumb">
                                <?php
                                    echo @$data->icon ?? '<i class="las la-star"></i>';
                                ?>
                            </div>
                            <h6 class="title"><?php echo e(__(@$data->title ?? 'Amenity')); ?></h6>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</section>
<!-- Our Ameninies Section Ends Here -->

<?php if(!app()->offsetExists('slick_script')): ?>
    <?php $__env->startPush('script-lib'); ?>
        <?php $__env->startPush('script-lib'); ?>
            <script src="<?php echo e(asset($activeTemplateTrue . 'js/slick.min.js')); ?>"></script>
        <?php $__env->stopPush(); ?>
    <?php $__env->stopPush(); ?>
    <?php app()->offsetSet('slick_script',true) ?>
<?php endif; ?>

<?php if(!app()->offsetExists('slick_css')): ?>
    <?php $__env->startPush('style-lib'); ?>
        <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'css/slick.css')); ?>">
    <?php $__env->stopPush(); ?>
    <?php app()->offsetSet('slick_css',true) ?>
<?php endif; ?>

<?php $__env->startPush('script'); ?>
    <script>
        $(function() {
            'use strict'

            $(".amenities-slider").slick({
                fade: false,
                slidesToShow: 6,
                slidesToScroll: 1,
                infinite: true,
                autoplay: true,
                pauseOnHover: true,
                centerMode: false,
                dots: false,
                arrows: false,
                // asNavFor: '.testimonial-img-slider',
                nextArrow: '<i class="las la-arrow-right arrow-right"></i>',
                prevArrow: '<i class="las la-arrow-left arrow-left"></i> ',
                responsive: [{
                        breakpoint: 1199,
                        settings: {
                            slidesToShow: 5,
                            slidesToScroll: 1,
                        },
                    },
                    {
                        breakpoint: 991,
                        settings: {
                            slidesToShow: 4,
                            slidesToScroll: 1,
                        },
                    },
                    {
                        breakpoint: 767,
                        settings: {
                            slidesToShow: 3,
                            slidesToScroll: 1,
                        },
                    },
                    {
                        breakpoint: 500,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 1,
                        },
                    },
                ],
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\sections\amenities.blade.php ENDPATH**/ ?>