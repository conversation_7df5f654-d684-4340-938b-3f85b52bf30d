<?php $__env->startSection('content'); ?>
<div class="container padding-top padding-bottom">
    <div class="row  justify-content-center">
        <div class="col-md-8">
            <div class="card cmn--card">
                <div class="card-body p-3 p-sm-4">
                    <div class="deposit-preview">
                        <div class="deposit-thumb">
                            <img src="<?php echo e($data->gatewayCurrency()->methodImage()); ?>" alt="<?php echo app('translator')->get('Image'); ?>">
                        </div>
                        <div class="deposit-content">
                            <ul>
                                <li>
                                    <?php echo app('translator')->get('Amount:'); ?> <span class="text--success"><?php echo e(showAmount($data->amount)); ?></span>
                                </li>
                                <li>
                                    <?php echo app('translator')->get('Charge:'); ?> <span class="text--danger"><?php echo e(showAmount($data->charge)); ?></span>
                                </li>
                                <li>
                                    <?php echo app('translator')->get('Payable:'); ?> <span class="text--warning"><?php echo e(showAmount($data->amount + $data->charge)); ?></span>
                                </li>
                                <li>
                                    <?php echo app('translator')->get('Conversion Rate:'); ?> <span class="text--info">1
                                        <?php echo e(__(gs('cur_text'))); ?>

                                        <?php echo app('translator')->get('='); ?> <?php echo e(showAmount($data->rate,currencyFormat:false)); ?>

                                        <?php echo e(__($data->baseCurrency())); ?></span>
                                </li>
                                <li>
                                    <?php echo app('translator')->get('In'); ?> <?php echo e($data->baseCurrency()); ?>: <span class="text--primary"><?php echo e(showAmount($data->final_amount,currencyFormat:false)); ?></span>
                                </li>
                            </ul>
                            <?php if(1000 > $data->method_code): ?>
                            <a href="<?php echo e(route('user.deposit.confirm')); ?>" class="btn btn--base btn--md w-100 radius-5 mt-3 "><?php echo app('translator')->get('pay now'); ?></a>
                            <?php else: ?>
                            <a href="<?php echo e(route('user.deposit.manual.confirm')); ?>" class="btn btn--base btn--md w-100 radius-5 mt-3 "><?php echo app('translator')->get('Pay
                                Now'); ?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate.'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\payment\preview.blade.php ENDPATH**/ ?>