<?php $__env->startSection('content'); ?>
    <!-- Blog Section Starts Here -->
    <section class="blog-section padding-top">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12">
                    <div class="row justify-content-center g-4">
                        <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-3 col-md-3 col-sm-10">
                                <div class="post-item">
                                    <div class="post-thumb">
                                        <img src="<?php echo e(getImage('assets/images/frontend/blog/thumb_' . $item->data_values->image)); ?>" alt="<?php echo e(__(@$item->data_values->title)); ?>">
                                    </div>
                                    <div class="post-content">
                                        <ul class="post-meta">
                                            <li>
                                                <span class="date"><i class="las la-calendar-check"></i><?php echo e(showDateTime($item->created_at, 'd M Y')); ?></span>
                                            </li>
                                        </ul>
                                        <h4 class="title"><a href="<?php echo e(route('blog.details', slug($item->data_values->title))); ?>"><?php echo e(__(@$item->data_values->title)); ?></a></h4>
                                        <p><?php echo e(__(strLimit(strip_tags($item->data_values->description), 50))); ?></p>

                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php if($blogs->hasPages()): ?>
                        <div class="custom-pagination">
                            <?php echo e(paginateLinks($blogs)); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section Ends Here -->
    <?php if($sections->secs != null): ?>
        <?php $__currentLoopData = json_decode($sections->secs); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make($activeTemplate . 'sections.' . $sec, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\blog.blade.php ENDPATH**/ ?>