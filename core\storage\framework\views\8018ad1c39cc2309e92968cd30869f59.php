<?php $__env->startSection('content'); ?>
    <div class="container padding-top padding-bottom">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card custom--card">
                    <div class="card-header">
                        <h5 class="card-title"><?php echo app('translator')->get('Paystack'); ?></h5>
                    </div>
                    <div class="card-body p-5">
                        <form action="<?php echo e(route('ipn.' . $deposit->gateway->alias)); ?>" method="POST" class="text-center">
                            <?php echo csrf_field(); ?>
                            <ul class="list-group text-center">
                                <li class="list-group-item d-flex justify-content-between">
                                    <?php echo app('translator')->get('You have to pay '); ?>:
                                    <strong><?php echo e(showAmount($deposit->final_amount, currencyFormat: false)); ?>

                                        <?php echo e(__($deposit->method_currency)); ?></strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <?php echo app('translator')->get('You will get '); ?>:
                                    <strong><?php echo e(showAmount($deposit->amount)); ?></strong>
                                </li>
                            </ul>
                            <button type="button" class="btn btn--base w-100 mt-3"
                                id="btn-confirm"><?php echo app('translator')->get('Pay Now'); ?></button>
                            <script src="//js.paystack.co/v1/inline.js" data-key="<?php echo e($data->key); ?>" data-email="<?php echo e($data->email); ?>"
                                data-amount="<?php echo e(round($data->amount)); ?>" data-currency="<?php echo e($data->currency); ?>" data-ref="<?php echo e($data->ref); ?>"
                                data-custom-button="btn-confirm"></script>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\payment\Paystack.blade.php ENDPATH**/ ?>