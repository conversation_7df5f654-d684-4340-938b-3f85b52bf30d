<div class="row">
    <?php $__currentLoopData = $stoppages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($item[0] != $item[1]): ?>
            <?php $sd = getStoppageInfo($item) ?>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="point-<?php echo e($loop->iteration); ?>" required><?php echo e($sd[0]->name); ?> - <?php echo e($sd[1]->name); ?></label>
                    <div class="input-group">
                        <div class="input-group-text"><?php echo e(__(gs('cur_text'))); ?></div>
                        <input type="number" class="form-control prices-auto numeric-validation" name="price[<?php echo e($sd[0]->id); ?>-<?php echo e($sd[1]->id); ?>]" required>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<script>
    'use strict';
    (function($) {
        $(".numeric-validation").keypress(function(e) {
            var unicode = e.charCode ? e.charCode : e.keyCode
            if (unicode != 8 && e.key != '.' && unicode != 45) {
                if ((unicode < 2534 || unicode > 2543) && (unicode < 48 || unicode > 57)) {
                    return false;
                }
            }
        });
    })(jQuery)
</script>
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\trip\ticket\route_data.blade.php ENDPATH**/ ?>