<?php $__env->startSection('content'); ?>
    <div class="verification-code-wrapper">
        <div class="verification-area">
            <form action="<?php echo e(route('user.verify.mobile')); ?>" method="POST" class="submit-form">
                <?php echo csrf_field(); ?>
                <p class="verification-text"><?php echo app('translator')->get('A 6 digit verification code sent to your mobile number'); ?> :
                    +<?php echo e(showMobileNumber(auth()->user()->mobileNumber)); ?></p>
                <?php echo $__env->make($activeTemplate . 'partials.verification_code', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <div class="mb-3">
                    <button type="submit" class="btn btn--base w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
                <div class="form-group">
                    <p>
                        <?php echo app('translator')->get('If you don\'t get any code'); ?>, <span class="countdown-wrapper"><?php echo app('translator')->get('try again after'); ?> <span id="countdown" class="fw-bold">--</span> <?php echo app('translator')->get('seconds'); ?></span> <a href="<?php echo e(route('user.send.verify.code', 'sms')); ?>" class="try-again-link d-none">
                            <?php echo app('translator')->get('Try again'); ?></a>
                    </p>
                    <a href="<?php echo e(route('user.logout')); ?>"><?php echo app('translator')->get('Logout'); ?></a>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script'); ?>
    <script>
        var distance = Number("<?php echo e(@$user->ver_code_send_at->addMinutes(2)->timestamp - time()); ?>");
        var x = setInterval(function() {
            distance--;
            document.getElementById("countdown").innerHTML = distance;
            if (distance <= 0) {
                clearInterval(x);
                document.querySelector('.countdown-wrapper').classList.add('d-none');
                document.querySelector('.try-again-link').classList.remove('d-none');
            }
        }, 1000);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.authenticate', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\auth\authorization\sms.blade.php ENDPATH**/ ?>