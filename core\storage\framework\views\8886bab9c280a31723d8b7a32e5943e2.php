<?php $__env->startSection('app'); ?>
    <?php echo $__env->yieldPushContent('fbComment'); ?>

    <?php echo $__env->make($activeTemplate . 'partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php if(!request()->routeIs('home') && !request()->routeIs('ticket') && !request()->routeIs('search')): ?>
        <?php echo $__env->make($activeTemplate . 'partials.breadcrumb', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    <?php echo $__env->yieldContent('content'); ?>

    <?php echo $__env->make($activeTemplate . 'partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php
        $cookie = App\Models\Frontend::where('data_keys', 'cookie.data')->first();
    ?>
    <?php if($cookie && $cookie->data_values && $cookie->data_values->status == 1 && !\Cookie::get('gdpr_cookie')): ?>
        <div id="cookiePolicy" class="cookies-card bg--default radius--10px text-center">
            <div class="cookies-card__icon">
                <i class="las la-cookie-bite"></i>
            </div>
            <p class="mt-4 cookies-card__content">
                <?php echo e(__($cookie->data_values->short_desc ?? 'We use cookies to improve your experience.')); ?>

                <a href="<?php echo e(route('cookie.policy')); ?>"><?php echo app('translator')->get('learn more'); ?></a>
            </p>
            <div class="cookies-card__btn mt-4">
                <a href="javascript:void(0)" class="btn policy btn--base w-100"><?php echo app('translator')->get('Allow'); ?></a>
            </div>
        </div>
    <?php endif; ?>

    <a href="javascript::void()" class="scrollToTop active"><i class="las la-chevron-up"></i></a>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        "use strict";

        $('.policy').on('click', function() {
            $.get('<?php echo e(route('cookie.accept')); ?>', function(response) {
                $('.cookies-card').addClass('d-none');
            });
        });

        setTimeout(function() {
            $('.cookies-card').removeClass('hide')
        }, 2000);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views/templates/basic/layouts/frontend.blade.php ENDPATH**/ ?>