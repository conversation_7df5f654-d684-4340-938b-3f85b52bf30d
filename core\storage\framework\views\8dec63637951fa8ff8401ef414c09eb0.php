<div class="row">
    <div class="col-md-12">
        <div class="card overflow-hidden">
            <div class="card-body p-0">
                <div class="table-responsive table-responsive--sm">
                    <table class="table align-items-center table--light">
                        <thead>
                        <tr>
                            <th><?php echo app('translator')->get('Short Code'); ?></th>
                            <th><?php echo app('translator')->get('Description'); ?></th>
                        </tr>
                        </thead>
                        <tbody class="list">
                            <?php $__currentLoopData = $template->shortcodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shortcode => $key): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                
                                <td><span class="short-codes"><?php echo "{{". $shortcode ."}}"  ?></span></td>
                                
                                <td><?php echo e(__($key)); ?></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php $__currentLoopData = gs('global_shortcodes'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shortCode => $codeDetails): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                
                                <td><span class="short-codes">{{<?php echo $shortCode ?>}}</span></td>
                                
                                <td><?php echo e(__($codeDetails)); ?></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div><!-- card end -->

    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\notification\template\shortcodes.blade.php ENDPATH**/ ?>