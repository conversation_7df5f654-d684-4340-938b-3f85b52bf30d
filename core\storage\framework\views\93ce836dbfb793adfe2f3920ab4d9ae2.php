<?php $__env->startSection('panel'); ?>
    <div class="row mb-none-30">
        <div class="col-xl-12 col-lg-12 col-md-12 mb-30">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <?php $__currentLoopData = $stoppages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $inserted = false;
                            ?>
                            <?php if($item[0] != $item[1]): ?>
                                <?php
                                    $sd = getStoppageInfo($item);
                                ?>

                                <?php $__currentLoopData = $ticketPrice->prices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($item[0] == $ticket->source_destination[0] && $item[1] == $ticket->source_destination[1]): ?>
                                        <?php
                                            $inserted = true;
                                        ?>
                                        <div class="col-lg-4 col-md-6 col-sm-6">
                                            <form action="<?php echo e(route('admin.trip.ticket.price.update', $ticket->id)); ?>" class="update-form">
                                                <?php echo csrf_field(); ?>
                                                <div class="form-group">
                                                    <label for="point-<?php echo e($loop->iteration); ?>"><?php echo e($sd[0]->name); ?> - <?php echo e($sd[1]->name); ?></label>
                                                    <div class="input-group">
                                                        <div class="input-group-text"><?php echo e(__(gs('cur_text'))); ?></div>
                                                        <input type="number" id="point-<?php echo e($loop->iteration); ?>" class="form-control prices-auto numeric-validation" name="price" min="0" required value="<?php echo e($ticket->price); ?>">
                                                        <button type="submit" class="btn btn--primary input-group-text update-price"><?php echo app('translator')->get('Update'); ?></button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if(!$inserted): ?>
                                    <div class="col-lg-4 col-md-6 col-sm-6">
                                        <form action="<?php echo e(route('admin.trip.ticket.price.update', 0)); ?>" class="update-form">
                                            <?php echo csrf_field(); ?>
                                            <div class="form-group">
                                                <label for="point-<?php echo e($loop->iteration); ?>"><?php echo e($sd[0]->name); ?> - <?php echo e($sd[1]->name); ?></label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><?php echo e(gs('cur_sym')); ?></span>
                                                    <input type="text" name="ticket_price" value="<?php echo e($ticketPrice->id); ?>" hidden>
                                                    <input type="text" name="source" value="<?php echo e($item[0]); ?>" hidden>
                                                    <input type="text" name="destination" value="<?php echo e($item[1]); ?>" hidden>
                                                    <input type="text" name="price" id="point-<?php echo e($loop->iteration); ?>" class="form-control prices-auto numeric-validation" placeholder="<?php echo app('translator')->get('Enter a price'); ?>" min="0" required />
                                                    <button type="submit" class="btn btn--primary input-group-text update-price"><?php echo app('translator')->get('Update'); ?></button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
    <?php if (isset($component)) { $__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.back','data' => ['route' => ''.e(route('admin.trip.ticket.price.index')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('back'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('admin.trip.ticket.price.index')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5)): ?>
<?php $attributes = $__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5; ?>
<?php unset($__attributesOriginal3b9bf6c313f6db4d5c9389e5666c89a5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5)): ?>
<?php $component = $__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5; ?>
<?php unset($__componentOriginal3b9bf6c313f6db4d5c9389e5666c89a5); ?>
<?php endif; ?>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        'use strict';
        (function($) {
            $(".numeric-validation").keypress(function(e) {
                var unicode = e.charCode ? e.charCode : e.keyCode;
                if (unicode != 8 && e.key != '.' && unicode != 45 && unicode != 13) {
                    if ((unicode < 2534 || unicode > 2543) && (unicode < 48 || unicode > 57)) {
                        return false;
                    }
                }
            });

            $(document).on('submit', '.update-form', function(e) {
                e.preventDefault();
                var data = $(this).serialize();
                let url = $(this).attr('action');

                if ($(this).find('[name="price"]').val() == '') {
                    notify('error', `<?php echo app('translator')->get('The price field is required'); ?>`);
                    return false;
                }

                $.ajax({
                    url: url,
                    method: "POST",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            notify('success', response.message);
                        } else {
                            notify('error', response.message);
                        }
                    }
                });
            });
        })(jQuery)
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\trip\ticket\edit_price.blade.php ENDPATH**/ ?>