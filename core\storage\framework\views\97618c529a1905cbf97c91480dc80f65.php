<?php $__env->startSection('panel'); ?>
    <?php
        $searchSettings = getContent('search_form.settings', true);
        $settings = $searchSettings ? $searchSettings->data_values : null;
    ?>
    
    <div class="row mb-none-30">
        <div class="col-lg-12 col-md-12 mb-30">
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="alert alert-info">
                                    <i class="las la-info-circle"></i>
                                    <strong><?php echo app('translator')->get('Search Form Redirection Settings'); ?></strong><br>
                                    <?php echo app('translator')->get('Configure how the search form redirects to external booking systems. Set the target domain and parameter names that will be used in the URL.'); ?>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('Redirect Domain'); ?></label>
                                    <input class="form-control" type="url" name="search_redirect_domain" 
                                           required value="<?php echo e(@$settings->redirect_domain); ?>" 
                                           placeholder="https://example.com">
                                    <small class="text-muted"><?php echo app('translator')->get('The domain where search results will be redirected (e.g., https://booking.example.com)'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Currency Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_currency_param"
                                           value="<?php echo e(@$settings->currency_param ?? 'ddCurrency'); ?>"
                                           placeholder="ddCurrency">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for currency (default: $ for Dollar)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('From Location Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_from_param"
                                           required value="<?php echo e(@$settings->from_param ?? 'ddFrom'); ?>"
                                           placeholder="ddFrom">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for departure location'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('To Location Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_to_param"
                                           required value="<?php echo e(@$settings->to_param ?? 'ddTo'); ?>"
                                           placeholder="ddTo">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for destination location'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="required"><?php echo app('translator')->get('Departure Date Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_departure_param"
                                           required value="<?php echo e(@$settings->departure_param ?? 'deptdate'); ?>"
                                           placeholder="deptdate">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for departure date (YYYY-MM-DD format)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Return Date Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_return_param"
                                           value="<?php echo e(@$settings->return_param ?? 'rtndate'); ?>"
                                           placeholder="rtndate">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for return date (optional for round trips)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Passengers Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_passengers_param"
                                           value="<?php echo e(@$settings->passengers_param ?? 'pax'); ?>"
                                           placeholder="pax">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for number of passengers (default: 1)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Trip Type Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_triptype_param"
                                           value="<?php echo e(@$settings->triptype_param ?? 'way'); ?>"
                                           placeholder="way">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for trip type (1=One Way, 2=Round Trip)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Transportation Type Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_transport_param"
                                           value="<?php echo e(@$settings->transport_param ?? 'type'); ?>"
                                           placeholder="type">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for transportation type (e.g., Bus)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('State/Region Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_state_param"
                                           value="<?php echo e(@$settings->state_param ?? 'sbf'); ?>"
                                           placeholder="sbf">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for state/region of departure city'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('From Filter Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_from_filter_param"
                                           value="<?php echo e(@$settings->from_filter_param ?? 'ddFromFilter'); ?>"
                                           placeholder="ddFromFilter">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for departure location filter (usually set to undefined)'); ?></small>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('To Filter Parameter'); ?></label>
                                    <input class="form-control" type="text" name="search_to_filter_param"
                                           value="<?php echo e(@$settings->to_filter_param ?? 'ddtofilter'); ?>"
                                           placeholder="ddtofilter">
                                    <small class="text-muted"><?php echo app('translator')->get('URL parameter name for destination location filter (usually set to undefined)'); ?></small>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0"><?php echo app('translator')->get('Example URL Structure'); ?></h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2"><strong><?php echo app('translator')->get('Generated URL will look like:'); ?></strong></p>
                                        <code id="example-url">
                                            <?php echo e(@$settings->redirect_domain ?? 'https://bus.superiortour.com.sg/booking-new/'); ?>?<?php echo e(@$settings->currency_param ?? 'ddCurrency'); ?>=%24&<?php echo e(@$settings->from_param ?? 'ddFrom'); ?>=Singapore&<?php echo e(@$settings->to_param ?? 'ddTo'); ?>=Legoland&<?php echo e(@$settings->departure_param ?? 'deptdate'); ?>=2025-09-30&<?php echo e(@$settings->return_param ?? 'rtndate'); ?>=2025-10-02&<?php echo e(@$settings->passengers_param ?? 'pax'); ?>=1&<?php echo e(@$settings->triptype_param ?? 'way'); ?>=2&<?php echo e(@$settings->from_filter_param ?? 'ddFromFilter'); ?>=undefined&<?php echo e(@$settings->to_filter_param ?? 'ddtofilter'); ?>=undefined&<?php echo e(@$settings->transport_param ?? 'type'); ?>=Bus&<?php echo e(@$settings->state_param ?? 'sbf'); ?>=Singapore
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn--primary w-100 h-45"><?php echo app('translator')->get('Submit'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
<script>
    'use strict';
    
    // Update example URL when inputs change
    function updateExampleUrl() {
        const domain = $('input[name="search_redirect_domain"]').val() || 'https://bus.superiortour.com.sg/booking-new/';
        const currencyParam = $('input[name="search_currency_param"]').val() || 'ddCurrency';
        const fromParam = $('input[name="search_from_param"]').val() || 'ddFrom';
        const toParam = $('input[name="search_to_param"]').val() || 'ddTo';
        const departureParam = $('input[name="search_departure_param"]').val() || 'deptdate';
        const returnParam = $('input[name="search_return_param"]').val() || 'rtndate';
        const passengersParam = $('input[name="search_passengers_param"]').val() || 'pax';
        const triptypeParam = $('input[name="search_triptype_param"]').val() || 'way';
        const fromFilterParam = $('input[name="search_from_filter_param"]').val() || 'ddFromFilter';
        const toFilterParam = $('input[name="search_to_filter_param"]').val() || 'ddtofilter';
        const transportParam = $('input[name="search_transport_param"]').val() || 'type';
        const stateParam = $('input[name="search_state_param"]').val() || 'sbf';

        const exampleUrl = `${domain}?${currencyParam}=%24&${fromParam}=Singapore&${toParam}=Legoland&${departureParam}=2025-09-30&${returnParam}=2025-10-02&${passengersParam}=1&${triptypeParam}=2&${fromFilterParam}=undefined&${toFilterParam}=undefined&${transportParam}=Bus&${stateParam}=Singapore`;

        $('#example-url').text(exampleUrl);
    }
    
    // Update example URL on input change
    $('input[name^="search_"]').on('input', updateExampleUrl);
    
    // Initial update
    updateExampleUrl();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views/admin/setting/search_form.blade.php ENDPATH**/ ?>