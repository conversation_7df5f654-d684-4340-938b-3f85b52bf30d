<?php $__env->startSection('panel'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive--sm table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th><?php echo app('translator')->get('Name'); ?></th>
                                    <th><?php echo app('translator')->get('Seat Layout'); ?></th>
                                    <th><?php echo app('translator')->get('No of Deck'); ?></th>
                                    <th><?php echo app('translator')->get('Total Seat'); ?></th>
                                    <th><?php echo app('translator')->get('Facilities'); ?></th>
                                    <th><?php echo app('translator')->get('Status'); ?></th>
                                    <th><?php echo app('translator')->get('Action'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $fleetType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e(__($item->name)); ?></td>
                                        <td><?php echo e(__($item->seat_layout)); ?></td>
                                        <td><?php echo e(__($item->deck)); ?></td>
                                        <td><?php echo e(array_sum($item->deck_seats)); ?></td>
                                        <td>
                                            <?php if($item->facilities): ?>
                                                <?php echo e(__(implode(',', $item->facilities))); ?>

                                            <?php else: ?>
                                                <?php echo app('translator')->get('No facilities'); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $item->statusBadge; ?></td>

                                        <td>
                                            <div class="button--group">
                                                <button type="button" class="btn btn-sm btn-outline--primary cuModalBtn" data-resource="<?php echo e($item); ?>" data-modal_title="<?php echo app('translator')->get('Edit Type'); ?>">
                                                    <i class="la la-pencil"></i><?php echo app('translator')->get('Edit'); ?>
                                                </button>

                                                <?php if(!$item->status): ?>
                                                    <button type="button" class="btn btn-sm btn-outline--success confirmationBtn" data-action="<?php echo e(route('admin.fleet.type.status', $item->id)); ?>" data-question="<?php echo app('translator')->get('Are you sure to enable this type?'); ?>">
                                                        <i class="la la-eye"></i><?php echo app('translator')->get('Enable'); ?>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline--danger  confirmationBtn" data-action="<?php echo e(route('admin.fleet.type.status', $item->id)); ?>" data-question="<?php echo app('translator')->get('Are you sure to disable this type?'); ?>">
                                                        <i class="la la-eye-slash"></i><?php echo app('translator')->get('Disable'); ?>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <?php if($fleetType->hasPages()): ?>
                    <div class="card-footer py-4">
                        <?php echo e(paginateLinks($fleetType)); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\ConfirmationModal::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>

    <div id="cuModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"></h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="<?php echo e(route('admin.fleet.type.store')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="form-group">
                            <label> <?php echo app('translator')->get('Name'); ?></label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="form-group">
                            <label> <?php echo app('translator')->get('Seat Layout'); ?></label>
                            <select name="seat_layout" class="form-control select2" data-minimum-results-for-search="-1">
                                <option value=""><?php echo app('translator')->get('Select an option'); ?></option>
                                <?php $__currentLoopData = $seatLayouts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item->layout); ?>"><?php echo e(__($item->layout)); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label> <?php echo app('translator')->get('No of Deck'); ?></label>
                            <input type="number" min="0" class="form-control" name="deck" required>
                        </div>
                        <div class="showSeat"></div>
                        <div class="form-group">
                            <label for="facilities"><?php echo app('translator')->get('Facilities'); ?></label>
                            <select class="select2-auto-tokenize" name="facilities[]" id="facilities" multiple="multiple">
                                <?php $__currentLoopData = $facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item->data_values->title); ?>">
                                        <?php echo e($item->data_values->title); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="inputName"><?php echo app('translator')->get('AC status'); ?></label>
                            <input type="checkbox" data-width="100%" data-height="40px" data-onstyle="-success" data-offstyle="-danger" data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('YES'); ?>" data-off="<?php echo app('translator')->get('NO'); ?>" name="has_ac">
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
    <button type="button" class="btn btn-sm btn-outline--primary cuModalBtn" data-modal_title="<?php echo app('translator')->get('Add New layout'); ?>">
        <i class="las la-plus"></i> <?php echo app('translator')->get('Add New'); ?>
    </button>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-lib'); ?>
    <script src="<?php echo e(asset('assets/admin/js/cu-modal.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {

            "use strict";

            $('input[name=deck]').on('input', function() {
                $('.showSeat').empty();
                for (var deck = 1; deck <= $(this).val(); deck++) {
                    $('.showSeat').append(`
                        <div class="form-group">
                            <label> Seats of Deck - ${deck} </label>
                            <input type="text" class="form-control hasArray" placeholder="<?php echo app('translator')->get('Enter Number of Seat'); ?>" name="deck_seats[]" required>
                        </div>
                    `);
                }
            })

            $('.cuModalBtn').on('click', function() {
                let modal = $('#cuModal');
                let data = $(this).data('resource');

                if (data.has_ac) {
                    modal.find('input[name=has_ac]').bootstrapToggle('on');
                } else {
                    modal.find('input[name=has_ac]').bootstrapToggle('off');
                }

                $('.showSeat').empty();
                if (data.deck) {
                    for (var i = 1; i <= data.deck; i++) {
                        $('.showSeat').append(`
                            <div class="form-group">
                                <label> Seats of Deck - ${i} </label>
                                <input type="text" class="form-control hasArray" placeholder="<?php echo app('translator')->get('Enter Number of Seat'); ?>" value="${data.deck_seats[i-1]}" name="deck_seats[]" required>
                            </div>
                        `);
                    }
                }

                if (data.facilities) {
                    $('#facilities').val(data.facilities).trigger("change");
                } else {
                    $('#facilities').val('').trigger("change");
                }
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\fleet\type.blade.php ENDPATH**/ ?>