<?php
    $faqContent = getContent('faq.content', true);
    $faqElements = getContent('faq.element', false, null, true);
?>

<section class="faq-section padding-top padding-bottom">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="section-header text-center">
                    <h2 class="title"><?php echo e(__(@$faqContent->data_values->heading)); ?></h2>
                    <p><?php echo e(__(@$faqContent->data_values->sub_heading)); ?></p>
                </div>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="faq-wrapper">
                    <?php $__currentLoopData = $faqElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($loop->odd): ?>
                            <div class="faq-item">
                                <div class="faq-title">
                                    <span class="icon"></span>
                                    <h5 class="title"><?php echo e(__(@$item->data_values->question)); ?></h5>
                                </div>
                                <div class="faq-content">
                                    <p><?php
                                        echo @$item->data_values->answer;
                                    ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="faq-wrapper">
                    <?php $__currentLoopData = $faqElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($loop->even): ?>
                            <div class="faq-item">
                                <div class="faq-title">
                                    <span class="icon"></span>
                                    <h5 class="title"><?php echo e(__(@$item->data_values->question)); ?></h5>
                                </div>
                                <div class="faq-content">
                                    <p><?php
                                        echo @$item->data_values->answer;
                                    ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\sections\faq.blade.php ENDPATH**/ ?>