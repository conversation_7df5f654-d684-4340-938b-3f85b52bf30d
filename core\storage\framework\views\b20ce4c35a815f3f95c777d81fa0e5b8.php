<?php
    $blogContent = getContent('blog.content', true);
    $blogElements = getContent('blog.element', false, 3);
?>

<!-- Blog Section Starts Here -->
<section class="blog-section padding-top padding-bottom">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-10">
                <div class="section-header text-center">
                    <h2 class="title"><?php echo e(__(@$blogContent->data_values->heading)); ?></h2>
                    <p><?php echo e(__(@$blogContent->data_values->sub_heading)); ?></p>
                </div>
            </div>
        </div>
        <div class="row justify-content-center g-4">
            <?php $__currentLoopData = $blogElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4 col-md-6 col-sm-10">
                <div class="post-item">
                    <div class="post-thumb">
                        <img src="<?php echo e(frontendImage('blog','thumb_'.(@$item->data_values->image ?? 'default.jpg'))); ?>" alt="blog">
                    </div>
                    <div class="post-content">
                        <ul class="post-meta">
                            <li>
                                <span class="date"><i class="las la-calendar-check"></i><?php echo e(showDateTime($item->created_at, 'd M Y')); ?></span>
                            </li>
                        </ul>
                        <h4 class="title"><a href="<?php echo e(route('blog.details',slug(@$item->data_values->title ?? 'default'))); ?>"><?php echo e(__(@$item->data_values->title ?? 'Blog Post')); ?></a></h4>
                        <p><?php echo e(__(strLimit(strip_tags(@$item->data_values->description ?? 'Blog description'),50))); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<!-- Blog Section Ends Here -->
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\sections\blog.blade.php ENDPATH**/ ?>