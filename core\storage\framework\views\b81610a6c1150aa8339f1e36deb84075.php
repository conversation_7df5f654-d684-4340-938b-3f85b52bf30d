<?php $__env->startSection('panel'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive--sm table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th><?php echo app('translator')->get('User'); ?></th>
                                    <th><?php echo app('translator')->get('PNR Number'); ?></th>
                                    <th><?php echo app('translator')->get('Journey Date'); ?></th>
                                    <th><?php echo app('translator')->get('Trip'); ?></th>
                                    <th><?php echo app('translator')->get('Pickup Point'); ?></th>
                                    <th><?php echo app('translator')->get('Dropping Point'); ?></th>
                                    <th><?php echo app('translator')->get('Status'); ?></th>
                                    <th><?php echo app('translator')->get('Ticket Count'); ?></th>
                                    <th><?php echo app('translator')->get('Fare'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $tickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td data-label="<?php echo app('translator')->get('User'); ?>">
                                            <span class="font-weight-bold"><?php echo e(__(@$item->user->fullname)); ?></span>
                                            <br>
                                            <span class="small"> <a href="<?php echo e(route('admin.users.detail', $item->user_id)); ?>"><span>@</span><?php echo e(__(@$item->user->username)); ?></a> </span>

                                        </td>
                                        <td data-label="<?php echo app('translator')->get('PNR Number'); ?>">
                                            <span class="text-muted"><?php echo e(__($item->pnr_number)); ?></span>
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Journey Date'); ?>">
                                            <?php echo e(__(showDateTime($item->date_of_journey, 'd M, Y'))); ?>

                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Trip'); ?>">
                                            <span class="font-weight-bold"><?php echo e(__($item->trip->fleetType->name)); ?></span>
                                            <br>
                                            <span class="font-weight-bold"> <?php echo e(__($item->trip->startFrom->name)); ?> - <?php echo e(__($item->trip->endTo->name)); ?></span>
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Pickup Point'); ?>">
                                            <?php echo e(__($item->pickup->name)); ?>

                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Dropping Point'); ?>">
                                            <?php echo e(__($item->drop->name)); ?>

                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Status'); ?>">
                                            <?php if($item->status == 1): ?>
                                                <span class="badge badge--success font-weight-normal text--samll"><?php echo app('translator')->get('Booked'); ?></span>
                                            <?php elseif($item->status == 2): ?>
                                                <span class="badge badge--warning font-weight-normal text--samll"><?php echo app('translator')->get('Pending'); ?></span>
                                            <?php else: ?>
                                                <span class="badge badge--danger font-weight-normal text--samll"><?php echo app('translator')->get('Rejected'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Ticket Count'); ?>">
                                            <?php echo e(__(sizeof($item->seats))); ?>

                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Fare'); ?>">
                                            <?php echo e(__(showAmount($item->sub_total))); ?>

                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php if($tickets->hasPages()): ?>
                    <div class="card-footer py-4">
                        <?php echo e(paginateLinks($tickets)); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('breadcrumb-plugins'); ?>
    <!-- Vehicle ticket search removed - booking system is external -->
    <div class="alert alert-info">
        <?php echo app('translator')->get('Booking system has been moved to external subdomain'); ?>
    </div>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\ticket\log.blade.php ENDPATH**/ ?>