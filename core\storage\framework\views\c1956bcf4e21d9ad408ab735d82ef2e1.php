<?php
    $content = getContent('footer.content', true);
    $socialLinks = getContent('social_links.element', orderById: true);
    $policies = getContent('policy_pages.element', orderById: true);
    $pages = App\Models\Page::where('tempname', $activeTemplate)
        ->where('is_default', Status::NO)
        ->get();
?>
<!-- Footer Section Starts Here -->
<section class="footer-section">
    <div class="footer-top">
        <div class="container">
            <div class="row footer-wrapper gy-sm-5 gy-4">
                <div class="col-xl-4 col-lg-3 col-md-6 col-sm-6">
                    <div class="footer-widget">
                        <div class="logo">
                            <img src="<?php echo e(siteLogo('dark')); ?>" alt="<?php echo app('translator')->get('Logo'); ?>">
                        </div>
                        <p><?php echo e(__(@$content->data_values->short_description)); ?></p>
                        <ul class="social-icons">
                            <?php $__currentLoopData = $socialLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a href="<?php echo e(@$item->data_values->url); ?>"><?php echo @$item->data_values->icon ?></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                    <div class="footer-widget">
                        <h4 class="widget-title"><?php echo app('translator')->get('Useful Links'); ?></h4>
                        <ul class="footer-links">
                            <?php $__currentLoopData = $pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a href="<?php echo e(route('pages', [$data->slug])); ?>"><?php echo e(__($data->name)); ?></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e(route('blog')); ?>"><?php echo app('translator')->get('Blog'); ?></a>
                            </li>
                            <li>
                                <a href="<?php echo e(route('contact')); ?>"><?php echo app('translator')->get('Contact'); ?></a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                    <div class="footer-widget">
                        <h4 class="widget-title"><?php echo app('translator')->get('Policies'); ?></h4>
                        <ul class="footer-links">
                            <?php $__currentLoopData = $policies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a
                                        href="<?php echo e(route('policy.pages', $policy->slug)); ?>"><?php
                                            echo @$policy->data_values->title;
                                        ?></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        </ul>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-4 col-sm-6">
                    <div class="footer-widget">
                        <h4 class="widget-title"><?php echo app('translator')->get('Contact Info'); ?></h4>
                        <?php
                            $contacts = getContent('contact.content', true);
                        ?>
                        <ul class="footer-contacts">
                            <li>
                                <i class="las la-map-pin"></i> <?php echo e(__(@$contacts->data_values->address)); ?>

                            </li>
                            <li>
                                <i class="las la-phone-volume"></i> <a
                                    href="tel:<?php echo e(__(@$contacts->data_values->contact_number)); ?>">
                                    <?php echo e(__(@$contacts->data_values->contact_number)); ?></a>
                            </li>
                            <li>
                                <i class="las la-envelope"></i> <a
                                    href="mailto:<?php echo e(__(@$contacts->data_values->email)); ?>">
                                    <?php echo e(__(@$contacts->data_values->email)); ?></a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Footer Section Ends Here -->


<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";


            $('.search').on('change', function() {
                $('#filterForm').submit();
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\partials\footer.blade.php ENDPATH**/ ?>