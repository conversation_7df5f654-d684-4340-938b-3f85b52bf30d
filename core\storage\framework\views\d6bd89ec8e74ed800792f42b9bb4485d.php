<?php $__env->startSection('panel'); ?>
    <div class="notify__area">
        <?php $__empty_1 = true; $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="notify-item-wrapper">
                <a class="notify__item <?php if($notification->is_read == Status::NO): ?> unread--notification <?php endif; ?>" href="<?php echo e(route('admin.notification.read', $notification->id)); ?>">
                    <div class="notify__content d-flex justify-content-between">
                        <div>
                            <h6 class="title"><?php echo e(__($notification->title)); ?></h6>
                            <span class="date"><i class="las la-clock"></i> <?php echo e(diffForHumans($notification->created_at)); ?></span>
                        </div>
                    </div>
                </a>
                <button type="button" class="btn btn-sm btn-outline--danger notify-delete-btn confirmationBtn" data-question="<?php echo app('translator')->get('Are you sure to delete the notification?'); ?>" data-action="<?php echo e(route('admin.notifications.delete.single',$notification->id)); ?>"><i class="las la-trash me-0"></i></button>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="card">
                <div class="card-body">
                    <div class="empty-notification-list text-center">
                        <img src="<?php echo e(getImage('assets/images/empty_list.png')); ?>" alt="empty">
                        <h5 class="text-muted"><?php echo app('translator')->get('No notification found.'); ?></h5>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <div class="mt-3">
            <?php echo e(paginateLinks($notifications)); ?>

        </div>
    </div>

    <?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\ConfirmationModal::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('breadcrumb-plugins'); ?>
    <?php if($hasUnread): ?>
        <a href="<?php echo e(route('admin.notifications.read.all')); ?>" class="btn btn-sm btn-outline--primary"><i class="las la-check"></i><?php echo app('translator')->get('Mark All as Read'); ?></a>
    <?php endif; ?>
    <?php if($hasNotification): ?>
        <button class="btn btn-sm btn-outline--danger confirmationBtn" data-action="<?php echo e(route('admin.notifications.delete.all')); ?>" data-question="<?php echo app('translator')->get('Are you sure to delete all notifications?'); ?>"><i class="las la-trash"></i><?php echo app('translator')->get('Delete all Notification'); ?></button>
    <?php endif; ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\admin\notifications.blade.php ENDPATH**/ ?>