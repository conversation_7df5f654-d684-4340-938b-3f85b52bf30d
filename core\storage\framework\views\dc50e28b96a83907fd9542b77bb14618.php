<?php $__env->startSection('content'); ?>
    <!-- booking history Starts Here -->
    <section class="dashboard-section padding-top padding-bottom">
        <div class="container">
            <div class="dashboard-wrapper">
                <div class="row pb-60 gy-4 justify-content-center">
                    <div class="col-lg-4 col-md-6 col-sm-10">
                        <div class="dashboard-widget">
                            <div class="dashboard-widget__content">
                                <p><?php echo app('translator')->get('Total Booked Ticket'); ?></p>
                                <h3 class="title"><?php echo e(__($widget['booked'])); ?></h3>
                            </div>
                            <div class="dashboard-widget__icon">
                                <i class="las la-ticket-alt"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-10">
                        <div class="dashboard-widget">
                            <div class="dashboard-widget__content">
                                <p><?php echo app('translator')->get('Total Rejected Ticket'); ?></p>
                                <h3 class="title"><?php echo e(__($widget['rejected'])); ?></h3>
                            </div>
                            <div class="dashboard-widget__icon">
                                <i class="las la-ticket-alt"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-10">
                        <div class="dashboard-widget">
                            <div class="dashboard-widget__content">
                                <p><?php echo app('translator')->get('Total Pending Ticket'); ?></p>
                                <h3 class="title"><?php echo e(__($widget['pending'])); ?></h3>
                            </div>
                            <div class="dashboard-widget__icon">
                                <i class="las la-ticket-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="booking-table-wrapper">
                    <table class="booking-table">
                        <thead>
                            <tr>
                                <th><?php echo app('translator')->get('PNR Number'); ?></th>
                                <th><?php echo app('translator')->get('AC / Non-Ac'); ?></th>
                                <th><?php echo app('translator')->get('Starting Point'); ?></th>
                                <th><?php echo app('translator')->get('Dropping Point'); ?></th>
                                <th><?php echo app('translator')->get('Journey Date'); ?></th>
                                <th><?php echo app('translator')->get('Pickup Time'); ?></th>
                                <th><?php echo app('translator')->get('Booked Seats'); ?></th>
                                <th><?php echo app('translator')->get('Status'); ?></th>
                                <th><?php echo app('translator')->get('Fare'); ?></th>
                                <th><?php echo app('translator')->get('Action'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $bookedTickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="ticket-no" data-label="<?php echo app('translator')->get('PNR Number'); ?>"><?php echo e(__($item->pnr_number)); ?></td>
                                    <td class="" data-label="<?php echo app('translator')->get('AC / Non-Ac'); ?>"><?php echo e($item->trip->fleetType->has_ac ? 'AC' : 'Non-Ac'); ?></td>
                                    <td class="pickup" data-label="Starting Point"><?php echo e(__($item->pickup->name)); ?></td>
                                    <td class="drop" data-label="Dropping Point"><?php echo e(__($item->drop->name)); ?></td>
                                    <td class="date" data-label="Journey Date"><?php echo e(__(showDateTime($item->date_of_journey, 'd M, Y'))); ?></td>
                                    <td class="time" data-label="Pickup Time"><?php echo e(__(showDateTime($item->trip->schedule->start_from, 'H:i a'))); ?></td>
                                    <td class="seats" data-label="Booked Seats"><?php echo e(__(implode(',', $item->seats))); ?></td>
                                    <td data-label="<?php echo app('translator')->get('Status'); ?>">
                                        <?php if($item->status == 1): ?>
                                            <span class="badge badge--success"> <?php echo app('translator')->get('Booked'); ?></span>
                                        <?php elseif($item->status == 2): ?>
                                            <span class="badge badge--warning"> <?php echo app('translator')->get('Pending'); ?></span>
                                        <?php else: ?>
                                            <span class="badge badge--danger"> <?php echo app('translator')->get('Rejected'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="fare" data-label="Fare"><?php echo e(__(showAmount($item->sub_total))); ?></td>
                                    <td class="action" data-label="Action">
                                        <div class="action-button-wrapper">
                                            <?php if($item->date_of_journey >= \Carbon\Carbon::today()->format('Y-m-d') && $item->status == 1): ?>
                                                <a href="<?php echo e(route('user.ticket.print', $item->id)); ?>" target="_blank" class="print"><i class="las la-print"></i></a>
                                            <?php else: ?>
                                                <a href="javascript::void(0)" class="checkinfo" data-info="<?php echo e($item); ?>" data-bs-toggle="modal" data-bs-target="#infoModal"><i class="las la-info-circle"></i></a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-center" colspan="100%"><?php echo e($emptyMessage); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <?php if($bookedTickets->hasPages()): ?>
                    <div class="custom-pagination">
                        <?php echo e(paginateLinks($bookedTickets)); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
    <!-- booking history end Here -->

    <div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"> <?php echo app('translator')->get('Ticket Booking History'); ?></h5>
                    <button type="button" class="w-auto btn--close" data-bs-dismiss="modal"><i class="las la-times"></i></button>
                </div>
                <div class="modal-body">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--danger w-auto btn--sm px-3" data-bs-dismiss="modal"></i>
                        <?php echo app('translator')->get('Close'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('style'); ?>
    <style>
        .modal-body p:not(:last-child) {
            border-bottom: 1px dashed #ebebeb;
            padding: 5px 0;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        "use strict"

        $('.checkinfo').on('click', function() {
            var info = $(this).data('info');
            var modal = $('#infoModal');
            var html = '';
            html += `
                    <p class="d-flex flex-wrap justify-content-between pt-0"><strong><?php echo app('translator')->get('Journey Date'); ?></strong>  <span>${info.date_of_journey}</span></p>
                    <p class="d-flex flex-wrap justify-content-between"><strong><?php echo app('translator')->get('PNR Number'); ?></strong>  <span>${info.pnr_number}</span></p>
                    <p class="d-flex flex-wrap justify-content-between"><strong><?php echo app('translator')->get('Route'); ?></strong>  <span>${info.trip.start_from.name} <?php echo app('translator')->get('to'); ?> ${info.trip.end_to.name}</span></p>
                    <p class="d-flex flex-wrap justify-content-between"><strong><?php echo app('translator')->get('Fare'); ?></strong>  <span>${parseInt(info.sub_total).toFixed(2)} <?php echo e(__(gs('cur_text'))); ?></span></p>
                    <p class="d-flex flex-wrap justify-content-between"><strong><?php echo app('translator')->get('Status'); ?></strong>  <span>${info.status == 1 ? '<span class="badge badge--success"><?php echo app('translator')->get('Successful'); ?></span>' : info.status == 2 ? '<span class="badge badge--warning"><?php echo app('translator')->get('Pending'); ?></span>' : '<span class="badge badge--danger"><?php echo app('translator')->get('Rejected'); ?></span>'}</span></p>
                `;
            modal.find('.modal-body').html(html);
        })
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\dashboard.blade.php ENDPATH**/ ?>