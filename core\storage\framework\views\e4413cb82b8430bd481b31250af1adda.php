<?php $__env->startSection('content'); ?>

<?php
    $content = getContent('contact.content',true);
?>

<section class="contact-section padding-top padding-bottom overflow-hidden">
    <div class="container">
        <div class="text-center">
            <h3 class="title mb-2"><?php echo e(__(@$content->data_values->title)); ?></h3>
            <p class="mb-5"><?php echo e(__(@$content->data_values->short_details)); ?></p>
        </div>
        <div class="row pb-80 gy-4 justify-content-center">
            <div class="col-sm-6 col-lg-4">
                <div class="info-item">
                    <div class="icon">
                        <i class="flaticon-location"></i>
                    </div>
                    <div class="content">
                        <h5 class="title"><?php echo app('translator')->get('Our Address'); ?></h5>
                        <?php echo app('translator')->get('Address'); ?> : <?php echo e(__(@$content->data_values->address )); ?>

                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-4">
                <div class="info-item active">
                    <div class="icon">
                        <i class="flaticon-call"></i>
                    </div>
                    <div class="content">
                        <h5 class="title"><?php echo app('translator')->get('Call Us'); ?></h5>
                        <a href="tel:<?php echo e(@$content->data_values->contact_number); ?>"><?php echo e(__(@$content->data_values->contact_number)); ?></a>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-4">
                <div class="info-item">
                    <div class="icon">
                        <i class="flaticon-envelope"></i>
                    </div>
                    <div class="content">
                        <h5 class="title"><?php echo app('translator')->get('Email Us'); ?></h5>
                        <a href="mailto:<?php echo e(@$content->data_values->email); ?>"><?php echo e(__(@$content->data_values->email)); ?></a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row gy-5">
            <div class="col-lg-6">
                <div class="contact-form-wrapper">
                    <h4 class="title mb-4"><?php echo e(@$content->data_values->form_title); ?></h4>
                    <form class="contact-form row gy-3" method="post">
                        <?php echo csrf_field(); ?>
                        <div class=" col-xl-6 col-lg-12 col-md-6">
                            <div class="form--group">
                                <label for="name"><?php echo app('translator')->get('Name'); ?> <span>*</span></label>
                                <input id="name" name="name" type="text" class="form--control" placeholder="<?php echo app('translator')->get('Name'); ?>" value="<?php echo e(auth()->user() ? auth()->user()->fullname : old('name')); ?>" <?php if(auth()->user()): ?> readonly <?php endif; ?> required>
                            </div>
                        </div>
                        <div class=" col-xl-6 col-lg-12 col-md-6">
                            <div class="form--group">
                                <label for="email"><?php echo app('translator')->get('Email'); ?> <span>*</span></label>
                                <input id="email" name="email" type="email" class="form--control" placeholder="<?php echo app('translator')->get('Email'); ?>" value="<?php echo e(auth()->user() ? auth()->user()->email : old('email')); ?>" <?php if(auth()->user()): ?> readonly <?php endif; ?> required>
                            </div>
                        </div>
                        <div class=" col-xl-12">
                            <div class="form--group">
                                <label for="subject"><?php echo app('translator')->get('Subject'); ?> <span>*</span></label>
                                <input id="subject" name="subject" type="text" class="form--control" placeholder="<?php echo app('translator')->get('Subject'); ?>" value="<?php echo e(old('subject')); ?>" required>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form--group">
                                <label for="msg"><?php echo app('translator')->get('Your Message'); ?> <span>*</span></label>
                                <textarea id="msg" name="message" class="form--control" placeholder="<?php echo app('translator')->get('Message'); ?>"><?php echo e(old('message')); ?></textarea>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form--group">
                                <button class="contact-button" type="submit"><?php echo app('translator')->get('Send Us Message'); ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="map-wrapper">
                    <iframe class="map" style="border:0;" src="https://maps.google.com/maps?q=<?php echo e(@$content->data_values->latitude); ?>,<?php echo e(@$content->data_values->longitude); ?>&hl=es;z=14&amp;output=embed"></iframe>
                </div>
            </div>
        </div>
    </div>
</section>



<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate.'layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\contact.blade.php ENDPATH**/ ?>