<?php $__env->startSection('content'); ?>
    <div class="container padding-top padding-bottom">
        <div class="row justify-content-center">
            <div class="col-xl-6 col-md-6">
                <div class="profile__content__edit p-0">
                    <div class="p-4">
                        <form action="" method="post" class="register">
                            <?php echo csrf_field(); ?>
                            <div class="row gy-3">
                                <div class="form-group col-12">
                                    <label class="form-label"><?php echo app('translator')->get('Current Password'); ?></label>
                                    <input type="password" class="form-control form--control" name="current_password" required autocomplete="current-password">
                                </div>
                                <div class="form-group col-12">
                                    <label class="form-label"><?php echo app('translator')->get('Password'); ?></label>
                                    <input type="password" class="form-control form--control <?php if(gs('secure_password')): ?> secure-password <?php endif; ?>" name="password" required autocomplete="current-password">
                                </div>
                                <div class="form-group col-12">
                                    <label class="form-label"><?php echo app('translator')->get('Confirm Password'); ?></label>
                                    <input type="password" class="form-control form--control" name="password_confirmation" required autocomplete="current-password">
                                </div>
                                <div class="form-group col-12">
                                    <button type="submit" class="btn btn--base w-100"><?php echo app('translator')->get('Submit'); ?></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php if(gs('secure_password')): ?>
    <?php $__env->startPush('script-lib'); ?>
        <script src="<?php echo e(asset('assets/global/js/secure_password.js')); ?>"></script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\password.blade.php ENDPATH**/ ?>