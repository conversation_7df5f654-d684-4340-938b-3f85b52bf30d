<?php $__env->startSection('content'); ?>
    <div class="container padding-top padding-bottom">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card custom--card">
                    <div class="card-header">
                        <h5><?php echo app('translator')->get('NMI'); ?></h5>
                    </div>
                    <div class="card-body">
                        <form role="form" class="disableSubmission appPayment" id="payment-form" method="<?php echo e($data->method); ?>" action="<?php echo e($data->url); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-12">
                                    <label class="form-label"><?php echo app('translator')->get('Card Number'); ?></label>
                                    <div class="input-group">
                                        <input type="tel" class="form-control form--control" name="billing-cc-number" autocomplete="off" value="<?php echo e(old('billing-cc-number')); ?>" required autofocus />
                                        <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <label class="form-label"><?php echo app('translator')->get('Expiration Date'); ?></label>
                                    <input type="tel" class="form-control form--control" name="billing-cc-exp" value="<?php echo e(old('billing-cc-exp')); ?>" placeholder="e.g. MM/YY" autocomplete="off" required />
                                </div>
                                <div class="col-md-6 ">
                                    <label class="form-label"><?php echo app('translator')->get('CVC Code'); ?></label>
                                    <input type="tel" class="form-control form--control" name="billing-cc-cvv" value="<?php echo e(old('billing-cc-cvv')); ?>" autocomplete="off" required />
                                </div>
                            </div>
                            <br>
                            <button class="btn btn--base w-100" type="submit"> <?php echo app('translator')->get('Submit'); ?></button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php if($deposit->from_api): ?>
    <?php $__env->startPush('script'); ?>
        <script>
            (function($) {
                "use strict";

                $('.appPayment').on('submit', function() {
                    $(this).find('[type=submit]').html('<i class="las la-spinner fa-spin"></i>');
                })
            })(jQuery);
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\viserbus\Files\core\resources\views\templates\basic\user\payment\NMI.blade.php ENDPATH**/ ?>