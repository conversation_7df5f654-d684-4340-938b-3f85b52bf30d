#!/bin/bash

# ViserBus cPanel 配置文件
# 请根据您的 cPanel 环境修改以下配置

# ===========================================
# 基本配置 - 请修改这些值
# ===========================================

# 您的域名 (不包含 http:// 或 https://)
DOMAIN="yourdomain.com"

# 您的 cPanel 用户名
CPANEL_USER="your_cpanel_username"

# 数据库配置 (cPanel 通常使用前缀)
DB_PREFIX="${CPANEL_USER}_"
DB_NAME="${DB_PREFIX}viserbus"
DB_USER="${DB_PREFIX}viserbus"
DB_PASS="your_secure_database_password"

# 管理员邮箱
ADMIN_EMAIL="admin@${DOMAIN}"

# ===========================================
# 高级配置 (通常不需要修改)
# ===========================================

# 项目路径 (cPanel 通常是 public_html)
PROJECT_PATH="$HOME/public_html"

# 备份目录
BACKUP_DIR="$HOME/viserbus_backup_$(date +%Y%m%d_%H%M%S)"

# 数据库主机 (cPanel 通常是 localhost)
DB_HOST="localhost"

# ===========================================
# 显示配置信息
# ===========================================

echo "🔧 ViserBus cPanel 部署配置"
echo "=================================="
echo "域名: $DOMAIN"
echo "cPanel 用户: $CPANEL_USER"
echo "数据库名: $DB_NAME"
echo "数据库用户: $DB_USER"
echo "项目路径: $PROJECT_PATH"
echo "管理员邮箱: $ADMIN_EMAIL"
echo "=================================="
echo ""
echo "📝 请确认以上信息正确，然后运行部署脚本："
echo "bash deploy-cpanel.sh"
