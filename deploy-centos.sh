#!/bin/bash

# ViserBus 自动部署脚本 - CentOS/RHEL
# 使用方法: chmod +x deploy-centos.sh && ./deploy-centos.sh

set -e  # 遇到错误立即退出

echo "🚀 开始 ViserBus 自动部署 (CentOS/RHEL)..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量 - 请根据您的环境修改
DOMAIN="yourdomain.com"
PROJECT_PATH="/var/www/html"
DB_NAME="viserbus_db"
DB_USER="viserbus_user"
DB_PASS="your_secure_password"
ADMIN_EMAIL="<EMAIL>"

echo -e "${BLUE}📋 部署配置:${NC}"
echo -e "域名: ${GREEN}$DOMAIN${NC}"
echo -e "项目路径: ${GREEN}$PROJECT_PATH${NC}"
echo -e "数据库: ${GREEN}$DB_NAME${NC}"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ 请使用 sudo 运行此脚本${NC}"
    exit 1
fi

# 1. 更新系统
echo -e "${YELLOW}📦 更新系统包...${NC}"
yum update -y

# 2. 安装 EPEL 仓库
echo -e "${YELLOW}📦 安装 EPEL 仓库...${NC}"
yum install -y epel-release

# 3. 安装 Remi 仓库 (用于 PHP 8.1)
echo -e "${YELLOW}📦 安装 Remi 仓库...${NC}"
yum install -y https://rpms.remirepo.net/enterprise/remi-release-$(rpm -E %rhel).rpm
yum-config-manager --enable remi-php81

# 4. 安装必需软件
echo -e "${YELLOW}🔧 安装必需软件...${NC}"
yum install -y nginx mariadb-server mariadb php php-fpm php-mysql php-xml php-gd php-mbstring php-curl php-zip php-bcmath php-json php-sqlite3 unzip curl wget

# 5. 安装 Composer
echo -e "${YELLOW}📥 安装 Composer...${NC}"
if ! command -v composer &> /dev/null; then
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    chmod +x /usr/local/bin/composer
fi

# 6. 启动并配置 MariaDB
echo -e "${YELLOW}🗄️ 配置 MariaDB...${NC}"
systemctl start mariadb
systemctl enable mariadb

# 安全配置 MariaDB (自动化)
mysql -e "UPDATE mysql.user SET Password = PASSWORD('root_password') WHERE User = 'root'"
mysql -e "DELETE FROM mysql.user WHERE User=''"
mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1')"
mysql -e "DROP DATABASE IF EXISTS test"
mysql -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%'"
mysql -e "FLUSH PRIVILEGES"

# 创建数据库和用户
mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';"
mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# 7. 创建项目目录
echo -e "${YELLOW}📁 创建项目目录...${NC}"
mkdir -p $PROJECT_PATH
cd $PROJECT_PATH

# 8. 设置文件权限
echo -e "${YELLOW}🔐 设置文件权限...${NC}"
chown -R nginx:nginx $PROJECT_PATH
chmod -R 755 $PROJECT_PATH
chmod -R 775 $PROJECT_PATH/core/storage
chmod -R 775 $PROJECT_PATH/core/bootstrap/cache

# 9. 配置 SELinux (如果启用)
echo -e "${YELLOW}🛡️ 配置 SELinux...${NC}"
if command -v getenforce &> /dev/null && [ "$(getenforce)" != "Disabled" ]; then
    setsebool -P httpd_can_network_connect 1
    setsebool -P httpd_execmem 1
    semanage fcontext -a -t httpd_exec_t "$PROJECT_PATH/core/storage(/.*)?"
    semanage fcontext -a -t httpd_exec_t "$PROJECT_PATH/core/bootstrap/cache(/.*)?"
    restorecon -Rv $PROJECT_PATH
fi

# 10. 配置环境文件
echo -e "${YELLOW}⚙️ 配置环境文件...${NC}"
cd $PROJECT_PATH/core
cp .env.production .env

# 更新 .env 文件
sed -i "s/APP_URL=.*/APP_URL=https:\/\/$DOMAIN/" .env
sed -i "s/APP_DEBUG=.*/APP_DEBUG=false/" .env
sed -i "s/APP_ENV=.*/APP_ENV=production/" .env
sed -i "s/DB_CONNECTION=.*/DB_CONNECTION=mysql/" .env
sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env
sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" .env
sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" .env
sed -i "s/MAIL_FROM_ADDRESS=.*/MAIL_FROM_ADDRESS=\"noreply@$DOMAIN\"/" .env

# 11. 安装 PHP 依赖
echo -e "${YELLOW}📦 安装 PHP 依赖...${NC}"
composer install --optimize-autoloader --no-dev

# 12. 生成应用缓存
echo -e "${YELLOW}⚡ 生成应用缓存...${NC}"
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 13. 配置 Nginx
echo -e "${YELLOW}🌐 配置 Nginx...${NC}"
cat > /etc/nginx/conf.d/$DOMAIN.conf << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    root $PROJECT_PATH;
    index index.php index.html;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # 主要重写规则
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # PHP 处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 隐藏敏感文件
    location ~ /\.env {
        deny all;
    }

    location ~ /\.ht {
        deny all;
    }
}
EOF

# 14. 配置 PHP-FPM
echo -e "${YELLOW}🔧 配置 PHP-FPM...${NC}"
sed -i 's/user = apache/user = nginx/' /etc/php-fpm.d/www.conf
sed -i 's/group = apache/group = nginx/' /etc/php-fpm.d/www.conf

# 15. 启动服务
echo -e "${YELLOW}🔄 启动服务...${NC}"
systemctl start nginx
systemctl start php-fpm
systemctl enable nginx
systemctl enable php-fpm

# 16. 配置防火墙
echo -e "${YELLOW}🔥 配置防火墙...${NC}"
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --reload

# 17. 安装 SSL 证书 (Let's Encrypt)
echo -e "${YELLOW}🔒 安装 SSL 证书...${NC}"
yum install -y certbot python3-certbot-nginx
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email $ADMIN_EMAIL

# 18. 设置定时任务
echo -e "${YELLOW}⏰ 设置定时任务...${NC}"
(crontab -l 2>/dev/null; echo "0 2 * * * certbot renew --quiet") | crontab -

# 19. 最终检查
echo -e "${YELLOW}🔍 最终检查...${NC}"
cd $PROJECT_PATH/core
php artisan --version

echo -e "${GREEN}✅ 部署完成!${NC}"
echo -e "${BLUE}🌐 您的网站: https://$DOMAIN${NC}"
echo -e "${BLUE}🔧 管理后台: https://$DOMAIN/admin${NC}"
echo -e "${YELLOW}📝 请记录以下信息:${NC}"
echo -e "数据库名: $DB_NAME"
echo -e "数据库用户: $DB_USER"
echo -e "数据库密码: $DB_PASS"

echo -e "${GREEN}🎉 ViserBus 部署成功!${NC}"
