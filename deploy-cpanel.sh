#!/bin/bash

# ViserBus 自动部署脚本 - cPanel 共享主机
# 使用方法: 上传到主机后运行 bash deploy-cpanel.sh

set -e  # 遇到错误立即退出

echo "🚀 开始 ViserBus 自动部署 (cPanel 共享主机)..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量 - 请根据您的环境修改
DOMAIN="yourdomain.com"
CPANEL_USER="your_cpanel_username"
DB_NAME="${CPANEL_USER}_viserbus"
DB_USER="${CPANEL_USER}_viserbus"
DB_PASS="your_secure_password"
ADMIN_EMAIL="<EMAIL>"

# 检测当前目录
CURRENT_DIR=$(pwd)
PUBLIC_HTML="$HOME/public_html"

echo -e "${BLUE}📋 部署配置:${NC}"
echo -e "域名: ${GREEN}$DOMAIN${NC}"
echo -e "cPanel 用户: ${GREEN}$CPANEL_USER${NC}"
echo -e "当前目录: ${GREEN}$CURRENT_DIR${NC}"
echo -e "网站目录: ${GREEN}$PUBLIC_HTML${NC}"

# 1. 检查 PHP 版本
echo -e "${YELLOW}🔍 检查 PHP 版本...${NC}"
PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2 | cut -d "." -f 1,2)
echo -e "PHP 版本: ${GREEN}$PHP_VERSION${NC}"

if [[ $(echo "$PHP_VERSION >= 8.1" | bc -l) -eq 0 ]]; then
    echo -e "${RED}❌ 需要 PHP 8.1 或更高版本${NC}"
    echo -e "${YELLOW}请在 cPanel 中将 PHP 版本设置为 8.1+${NC}"
    exit 1
fi

# 2. 检查必需的 PHP 扩展
echo -e "${YELLOW}🔍 检查 PHP 扩展...${NC}"
REQUIRED_EXTENSIONS=("bcmath" "ctype" "fileinfo" "json" "mbstring" "openssl" "pdo" "tokenizer" "xml" "gd")

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "$ext"; then
        echo -e "✅ $ext"
    else
        echo -e "${RED}❌ $ext 扩展缺失${NC}"
        echo -e "${YELLOW}请在 cPanel 中启用 $ext 扩展${NC}"
    fi
done

# 3. 检查 Composer
echo -e "${YELLOW}📦 检查 Composer...${NC}"
if ! command -v composer &> /dev/null; then
    echo -e "${YELLOW}📥 安装 Composer...${NC}"
    cd $HOME
    curl -sS https://getcomposer.org/installer | php
    mkdir -p $HOME/bin
    mv composer.phar $HOME/bin/composer
    chmod +x $HOME/bin/composer
    export PATH="$HOME/bin:$PATH"
    echo 'export PATH="$HOME/bin:$PATH"' >> $HOME/.bashrc
fi

# 4. 备份现有文件 (如果存在)
echo -e "${YELLOW}💾 备份现有文件...${NC}"
if [ -d "$PUBLIC_HTML" ]; then
    BACKUP_DIR="$HOME/backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    cp -r $PUBLIC_HTML/* $BACKUP_DIR/ 2>/dev/null || true
    echo -e "备份保存在: ${GREEN}$BACKUP_DIR${NC}"
fi

# 5. 清理 public_html 目录
echo -e "${YELLOW}🧹 清理网站目录...${NC}"
rm -rf $PUBLIC_HTML/*
rm -rf $PUBLIC_HTML/.[^.]*

# 6. 复制项目文件
echo -e "${YELLOW}📁 复制项目文件...${NC}"
if [ -d "$CURRENT_DIR/Files" ]; then
    cp -r $CURRENT_DIR/Files/* $PUBLIC_HTML/
else
    echo -e "${RED}❌ 找不到 Files 目录${NC}"
    echo -e "${YELLOW}请确保脚本在包含 Files 目录的文件夹中运行${NC}"
    exit 1
fi

# 7. 设置文件权限
echo -e "${YELLOW}🔐 设置文件权限...${NC}"
cd $PUBLIC_HTML
chmod -R 755 .
chmod -R 775 core/storage
chmod -R 775 core/bootstrap/cache
chmod 644 .htaccess

# 8. 配置环境文件
echo -e "${YELLOW}⚙️ 配置环境文件...${NC}"
cd $PUBLIC_HTML/core

# 复制生产环境配置
cp .env.production .env

# 更新 .env 文件
sed -i "s/APP_URL=.*/APP_URL=https:\/\/$DOMAIN/" .env
sed -i "s/APP_DEBUG=.*/APP_DEBUG=false/" .env
sed -i "s/APP_ENV=.*/APP_ENV=production/" .env

# 数据库配置 - 共享主机通常使用 MySQL
sed -i "s/DB_CONNECTION=.*/DB_CONNECTION=mysql/" .env
sed -i "s/DB_HOST=.*/DB_HOST=localhost/" .env
sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env
sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" .env
sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" .env

# 邮件配置
sed -i "s/MAIL_FROM_ADDRESS=.*/MAIL_FROM_ADDRESS=\"noreply@$DOMAIN\"/" .env

# 9. 安装 PHP 依赖
echo -e "${YELLOW}📦 安装 PHP 依赖...${NC}"
if command -v composer &> /dev/null; then
    composer install --optimize-autoloader --no-dev
else
    echo -e "${RED}❌ Composer 未找到${NC}"
    echo -e "${YELLOW}请手动安装 Composer 或联系主机提供商${NC}"
fi

# 10. 数据库处理
echo -e "${YELLOW}🗄️ 数据库配置...${NC}"
echo -e "${BLUE}数据库信息:${NC}"
echo -e "数据库名: ${GREEN}$DB_NAME${NC}"
echo -e "数据库用户: ${GREEN}$DB_USER${NC}"
echo -e "数据库密码: ${GREEN}$DB_PASS${NC}"
echo -e "${YELLOW}请在 cPanel 中创建以上数据库和用户${NC}"

# 如果要保持使用 SQLite (推荐用于共享主机)
echo -e "${BLUE}或者保持使用 SQLite (推荐):${NC}"
sed -i "s/DB_CONNECTION=.*/DB_CONNECTION=sqlite/" .env
sed -i "s|DB_DATABASE=.*|DB_DATABASE=$PUBLIC_HTML/core/database/database.sqlite|" .env

# 11. 生成应用缓存
echo -e "${YELLOW}⚡ 生成应用缓存...${NC}"
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 12. 创建 .htaccess 文件 (如果不存在)
echo -e "${YELLOW}🌐 配置 .htaccess...${NC}"
if [ ! -f "$PUBLIC_HTML/.htaccess" ]; then
    cat > $PUBLIC_HTML/.htaccess << 'EOF'
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]

    <Files .env>
        Order allow,deny
        Deny from all
    </Files>
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
</IfModule>
EOF
fi

# 13. 最终检查
echo -e "${YELLOW}🔍 最终检查...${NC}"
cd $PUBLIC_HTML/core
php artisan --version

echo -e "${GREEN}✅ 部署完成!${NC}"
echo -e "${BLUE}🌐 您的网站: https://$DOMAIN${NC}"
echo -e "${BLUE}🔧 管理后台: https://$DOMAIN/admin${NC}"

echo -e "${YELLOW}📝 后续步骤:${NC}"
echo -e "1. 在 cPanel 中创建数据库: $DB_NAME"
echo -e "2. 创建数据库用户: $DB_USER"
echo -e "3. 设置数据库密码: $DB_PASS"
echo -e "4. 将用户添加到数据库并授予所有权限"
echo -e "5. 如果使用 MySQL，请导入数据"
echo -e "6. 在 cPanel 中设置 SSL 证书"
echo -e "7. 测试网站功能"

echo -e "${GREEN}🎉 ViserBus 部署成功!${NC}"
