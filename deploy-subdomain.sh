#!/bin/bash

# ViserBus 子域名部署脚本
# 专门部署到 superiortour.com.my 子域名
# 使用方法: bash deploy-subdomain.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署 ViserBus 到子域名..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量 - 请根据您的环境修改
DOMAIN="superiortour.com.my"
CPANEL_USER="your_cpanel_username"
DB_PREFIX="${CPANEL_USER}_"
DB_NAME="${DB_PREFIX}superiortour"
DB_USER="${DB_PREFIX}superiortour"
DB_PASS="your_secure_database_password"
ADMIN_EMAIL="admin@${DOMAIN}"

# 子域名路径配置
PUBLIC_HTML="$HOME/public_html"
SUBDOMAIN_PATH="$PUBLIC_HTML/superiortour.com.my"  # 根据实际情况调整

# 检测当前目录
CURRENT_DIR=$(pwd)

echo -e "${BLUE}📋 部署配置:${NC}"
echo -e "域名: ${GREEN}$DOMAIN${NC}"
echo -e "cPanel 用户: ${GREEN}$CPANEL_USER${NC}"
echo -e "子域名路径: ${GREEN}$SUBDOMAIN_PATH${NC}"
echo -e "当前目录: ${GREEN}$CURRENT_DIR${NC}"

# 1. 检查子域名目录
echo -e "${YELLOW}🔍 检查子域名目录...${NC}"
if [ ! -d "$SUBDOMAIN_PATH" ]; then
    echo -e "${YELLOW}创建子域名目录: $SUBDOMAIN_PATH${NC}"
    mkdir -p "$SUBDOMAIN_PATH"
fi

# 2. 检查 PHP 版本
echo -e "${YELLOW}🔍 检查 PHP 版本...${NC}"
PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2 | cut -d "." -f 1,2)
echo -e "PHP 版本: ${GREEN}$PHP_VERSION${NC}"

if [[ $(echo "$PHP_VERSION >= 8.1" | bc -l) -eq 0 ]]; then
    echo -e "${RED}❌ 需要 PHP 8.1 或更高版本${NC}"
    echo -e "${YELLOW}请在 cPanel 中将 PHP 版本设置为 8.1+${NC}"
    exit 1
fi

# 3. 检查必需的 PHP 扩展
echo -e "${YELLOW}🔍 检查 PHP 扩展...${NC}"
REQUIRED_EXTENSIONS=("bcmath" "ctype" "fileinfo" "json" "mbstring" "openssl" "pdo" "tokenizer" "xml" "gd")

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "$ext"; then
        echo -e "✅ $ext"
    else
        echo -e "${RED}❌ $ext 扩展缺失${NC}"
        echo -e "${YELLOW}请在 cPanel 中启用 $ext 扩展${NC}"
    fi
done

# 4. 检查 Composer
echo -e "${YELLOW}📦 检查 Composer...${NC}"
if ! command -v composer &> /dev/null; then
    echo -e "${YELLOW}📥 安装 Composer...${NC}"
    cd $HOME
    curl -sS https://getcomposer.org/installer | php
    mkdir -p $HOME/bin
    mv composer.phar $HOME/bin/composer
    chmod +x $HOME/bin/composer
    export PATH="$HOME/bin:$PATH"
    echo 'export PATH="$HOME/bin:$PATH"' >> $HOME/.bashrc
fi

# 5. 清理子域名目录
echo -e "${YELLOW}🧹 清理子域名目录...${NC}"
rm -rf $SUBDOMAIN_PATH/*
rm -rf $SUBDOMAIN_PATH/.[^.]*

# 6. 复制项目文件
echo -e "${YELLOW}📁 复制项目文件...${NC}"
if [ -d "$CURRENT_DIR/Files" ]; then
    cp -r $CURRENT_DIR/Files/* $SUBDOMAIN_PATH/
elif [ -f "$CURRENT_DIR/index.php" ]; then
    # 如果当前目录就是 Files 目录
    cp -r $CURRENT_DIR/* $SUBDOMAIN_PATH/
else
    echo -e "${RED}❌ 找不到 ViserBus 文件${NC}"
    echo -e "${YELLOW}请确保脚本在包含项目文件的目录中运行${NC}"
    exit 1
fi

# 7. 设置文件权限
echo -e "${YELLOW}🔐 设置文件权限...${NC}"
cd $SUBDOMAIN_PATH
chmod -R 755 .
chmod -R 775 core/storage
chmod -R 775 core/bootstrap/cache
chmod 644 .htaccess

# 8. 配置环境文件
echo -e "${YELLOW}⚙️ 配置环境文件...${NC}"
cd $SUBDOMAIN_PATH/core

# 复制生产环境配置
if [ -f ".env.production" ]; then
    cp .env.production .env
elif [ -f ".env.cpanel" ]; then
    cp .env.cpanel .env
else
    echo -e "${RED}❌ 找不到环境配置模板${NC}"
    exit 1
fi

# 更新 .env 文件
sed -i "s/APP_URL=.*/APP_URL=https:\/\/$DOMAIN/" .env
sed -i "s/APP_DEBUG=.*/APP_DEBUG=false/" .env
sed -i "s/APP_ENV=.*/APP_ENV=production/" .env

# 数据库配置 - 使用 SQLite (推荐用于子域名)
sed -i "s/DB_CONNECTION=.*/DB_CONNECTION=sqlite/" .env
sed -i "s|DB_DATABASE=.*|DB_DATABASE=$SUBDOMAIN_PATH/core/database/database.sqlite|" .env

# 邮件配置
sed -i "s/MAIL_FROM_ADDRESS=.*/MAIL_FROM_ADDRESS=\"noreply@$DOMAIN\"/" .env

# 9. 安装 PHP 依赖
echo -e "${YELLOW}📦 安装 PHP 依赖...${NC}"
if command -v composer &> /dev/null; then
    composer install --optimize-autoloader --no-dev
else
    echo -e "${RED}❌ Composer 未找到${NC}"
    echo -e "${YELLOW}请手动安装 Composer 或联系主机提供商${NC}"
fi

# 10. 生成应用缓存
echo -e "${YELLOW}⚡ 生成应用缓存...${NC}"
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 11. 创建 .htaccess 文件 (如果不存在)
echo -e "${YELLOW}🌐 配置 .htaccess...${NC}"
if [ ! -f "$SUBDOMAIN_PATH/.htaccess" ]; then
    cat > $SUBDOMAIN_PATH/.htaccess << 'EOF'
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]

    <Files .env>
        Order allow,deny
        Deny from all
    </Files>
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
</IfModule>
EOF
fi

# 12. 最终检查
echo -e "${YELLOW}🔍 最终检查...${NC}"
cd $SUBDOMAIN_PATH/core
php artisan --version

echo -e "${GREEN}✅ 部署完成!${NC}"
echo -e "${BLUE}🌐 子域名网站: https://$DOMAIN${NC}"
echo -e "${BLUE}🔧 管理后台: https://$DOMAIN/admin${NC}"

echo -e "${YELLOW}📝 后续步骤:${NC}"
echo -e "1. 在 cPanel 中确认子域名指向正确目录"
echo -e "2. 设置 SSL 证书 (Let's Encrypt)"
echo -e "3. 测试网站功能"
echo -e "4. 配置邮件设置 (如需要)"

echo -e "${GREEN}🎉 ViserBus 子域名部署成功!${NC}"
