#!/bin/bash

# ViserBus 自动部署脚本 - Ubuntu/Debian
# 使用方法: chmod +x deploy-ubuntu.sh && ./deploy-ubuntu.sh

set -e  # 遇到错误立即退出

echo "🚀 开始 ViserBus 自动部署..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量 - 请根据您的环境修改
DOMAIN="yourdomain.com"
PROJECT_PATH="/var/www/html"
DB_NAME="viserbus_db"
DB_USER="viserbus_user"
DB_PASS="your_secure_password"
ADMIN_EMAIL="<EMAIL>"

echo -e "${BLUE}📋 部署配置:${NC}"
echo -e "域名: ${GREEN}$DOMAIN${NC}"
echo -e "项目路径: ${GREEN}$PROJECT_PATH${NC}"
echo -e "数据库: ${GREEN}$DB_NAME${NC}"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ 请使用 sudo 运行此脚本${NC}"
    exit 1
fi

# 1. 更新系统
echo -e "${YELLOW}📦 更新系统包...${NC}"
apt update && apt upgrade -y

# 2. 安装必需软件
echo -e "${YELLOW}🔧 安装必需软件...${NC}"
apt install -y nginx mysql-server php8.1 php8.1-fpm php8.1-mysql php8.1-xml php8.1-gd php8.1-mbstring php8.1-curl php8.1-zip php8.1-bcmath php8.1-tokenizer php8.1-json php8.1-sqlite3 unzip curl wget

# 3. 安装 Composer
echo -e "${YELLOW}📥 安装 Composer...${NC}"
if ! command -v composer &> /dev/null; then
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    chmod +x /usr/local/bin/composer
fi

# 4. 配置 MySQL
echo -e "${YELLOW}🗄️ 配置 MySQL...${NC}"
mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';"
mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# 5. 创建项目目录
echo -e "${YELLOW}📁 创建项目目录...${NC}"
mkdir -p $PROJECT_PATH
cd $PROJECT_PATH

# 6. 设置文件权限
echo -e "${YELLOW}🔐 设置文件权限...${NC}"
chown -R www-data:www-data $PROJECT_PATH
chmod -R 755 $PROJECT_PATH
chmod -R 775 $PROJECT_PATH/core/storage
chmod -R 775 $PROJECT_PATH/core/bootstrap/cache

# 7. 配置环境文件
echo -e "${YELLOW}⚙️ 配置环境文件...${NC}"
cd $PROJECT_PATH/core
cp .env.production .env

# 更新 .env 文件
sed -i "s/APP_URL=.*/APP_URL=https:\/\/$DOMAIN/" .env
sed -i "s/APP_DEBUG=.*/APP_DEBUG=false/" .env
sed -i "s/APP_ENV=.*/APP_ENV=production/" .env
sed -i "s/DB_CONNECTION=.*/DB_CONNECTION=mysql/" .env
sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env
sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" .env
sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" .env
sed -i "s/MAIL_FROM_ADDRESS=.*/MAIL_FROM_ADDRESS=\"noreply@$DOMAIN\"/" .env

# 8. 安装 PHP 依赖
echo -e "${YELLOW}📦 安装 PHP 依赖...${NC}"
composer install --optimize-autoloader --no-dev

# 9. 数据库迁移 (如果需要从 SQLite 迁移到 MySQL)
echo -e "${YELLOW}🔄 数据库设置...${NC}"
# 这里您可以选择保持 SQLite 或迁移到 MySQL
# 如果保持 SQLite，注释掉上面的 MySQL 配置部分

# 10. 生成应用缓存
echo -e "${YELLOW}⚡ 生成应用缓存...${NC}"
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 11. 配置 Nginx
echo -e "${YELLOW}🌐 配置 Nginx...${NC}"
cat > /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    root $PROJECT_PATH;
    index index.php index.html;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # 主要重写规则
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # PHP 处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 隐藏敏感文件
    location ~ /\.env {
        deny all;
    }

    location ~ /\.ht {
        deny all;
    }
}
EOF

# 启用站点
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 测试 Nginx 配置
nginx -t

# 12. 启动服务
echo -e "${YELLOW}🔄 启动服务...${NC}"
systemctl restart nginx
systemctl restart php8.1-fpm
systemctl enable nginx
systemctl enable php8.1-fpm

# 13. 安装 SSL 证书 (Let's Encrypt)
echo -e "${YELLOW}🔒 安装 SSL 证书...${NC}"
apt install -y certbot python3-certbot-nginx
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email $ADMIN_EMAIL

# 14. 设置定时任务
echo -e "${YELLOW}⏰ 设置定时任务...${NC}"
(crontab -l 2>/dev/null; echo "0 2 * * * certbot renew --quiet") | crontab -

# 15. 最终检查
echo -e "${YELLOW}🔍 最终检查...${NC}"
cd $PROJECT_PATH/core
php artisan --version

echo -e "${GREEN}✅ 部署完成!${NC}"
echo -e "${BLUE}🌐 您的网站: https://$DOMAIN${NC}"
echo -e "${BLUE}🔧 管理后台: https://$DOMAIN/admin${NC}"
echo -e "${YELLOW}📝 请记录以下信息:${NC}"
echo -e "数据库名: $DB_NAME"
echo -e "数据库用户: $DB_USER"
echo -e "数据库密码: $DB_PASS"

echo -e "${GREEN}🎉 ViserBus 部署成功!${NC}"
