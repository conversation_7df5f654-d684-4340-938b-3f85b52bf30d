#!/bin/bash

# ViserBus 数据库迁移脚本 - SQLite 到 MySQL
# 使用方法: bash migrate-database.sh

set -e  # 遇到错误立即退出

echo "🔄 开始数据库迁移 (SQLite → MySQL)..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量 - 请根据您的环境修改
SQLITE_DB="core/database/database.sqlite"
MYSQL_HOST="localhost"
MYSQL_DB="viserbus_db"
MYSQL_USER="viserbus_user"
MYSQL_PASS="your_secure_password"
BACKUP_DIR="database_backup_$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}📋 迁移配置:${NC}"
echo -e "SQLite 数据库: ${GREEN}$SQLITE_DB${NC}"
echo -e "MySQL 数据库: ${GREEN}$MYSQL_DB${NC}"
echo -e "MySQL 用户: ${GREEN}$MYSQL_USER${NC}"

# 1. 检查 SQLite 数据库是否存在
echo -e "${YELLOW}🔍 检查 SQLite 数据库...${NC}"
if [ ! -f "$SQLITE_DB" ]; then
    echo -e "${RED}❌ SQLite 数据库文件不存在: $SQLITE_DB${NC}"
    exit 1
fi

# 2. 检查 MySQL 连接
echo -e "${YELLOW}🔍 检查 MySQL 连接...${NC}"
if ! mysql -h"$MYSQL_HOST" -u"$MYSQL_USER" -p"$MYSQL_PASS" -e "SELECT 1;" &>/dev/null; then
    echo -e "${RED}❌ 无法连接到 MySQL 数据库${NC}"
    echo -e "${YELLOW}请检查 MySQL 配置信息${NC}"
    exit 1
fi

# 3. 创建备份目录
echo -e "${YELLOW}💾 创建备份...${NC}"
mkdir -p "$BACKUP_DIR"
cp "$SQLITE_DB" "$BACKUP_DIR/"

# 4. 导出 SQLite 数据
echo -e "${YELLOW}📤 导出 SQLite 数据...${NC}"

# 导出表结构和数据
sqlite3 "$SQLITE_DB" .dump > "$BACKUP_DIR/sqlite_dump.sql"

# 转换 SQLite 语法到 MySQL 语法
echo -e "${YELLOW}🔄 转换数据格式...${NC}"

# 创建 MySQL 兼容的 SQL 文件
cat > "$BACKUP_DIR/mysql_import.sql" << 'EOF'
-- MySQL 数据导入脚本
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

EOF

# 处理 SQLite dump 文件，转换为 MySQL 格式
sed -e 's/PRAGMA foreign_keys=OFF;//g' \
    -e 's/BEGIN TRANSACTION;//g' \
    -e 's/COMMIT;//g' \
    -e 's/sqlite_sequence/mysql_sequence/g' \
    -e 's/AUTOINCREMENT/AUTO_INCREMENT/g' \
    -e 's/INTEGER PRIMARY KEY/INT AUTO_INCREMENT PRIMARY KEY/g' \
    -e 's/TEXT/VARCHAR(255)/g' \
    -e 's/REAL/DECIMAL(10,2)/g' \
    -e 's/BLOB/LONGBLOB/g' \
    "$BACKUP_DIR/sqlite_dump.sql" >> "$BACKUP_DIR/mysql_import.sql"

# 添加结束语句
cat >> "$BACKUP_DIR/mysql_import.sql" << 'EOF'

COMMIT;
SET FOREIGN_KEY_CHECKS = 1;
EOF

# 5. 清空 MySQL 数据库
echo -e "${YELLOW}🧹 清空 MySQL 数据库...${NC}"
mysql -h"$MYSQL_HOST" -u"$MYSQL_USER" -p"$MYSQL_PASS" "$MYSQL_DB" -e "
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS admins;
DROP TABLE IF EXISTS admin_notifications;
DROP TABLE IF EXISTS admin_password_resets;
DROP TABLE IF EXISTS cache;
DROP TABLE IF EXISTS cache_locks;
DROP TABLE IF EXISTS counters;
DROP TABLE IF EXISTS deposits;
DROP TABLE IF EXISTS device_tokens;
DROP TABLE IF EXISTS extensions;
DROP TABLE IF EXISTS forms;
DROP TABLE IF EXISTS frontends;
DROP TABLE IF EXISTS gateways;
DROP TABLE IF EXISTS gateway_currencies;
DROP TABLE IF EXISTS general_settings;
DROP TABLE IF EXISTS languages;
DROP TABLE IF EXISTS notification_logs;
DROP TABLE IF EXISTS notification_templates;
DROP TABLE IF EXISTS pages;
DROP TABLE IF EXISTS personal_access_tokens;
DROP TABLE IF EXISTS support_attachments;
DROP TABLE IF EXISTS support_messages;
DROP TABLE IF EXISTS support_tickets;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS update_logs;
DROP TABLE IF EXISTS user_logins;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS vehicle_routes;
DROP TABLE IF EXISTS vehicles;
DROP TABLE IF EXISTS vehicle_seats;
DROP TABLE IF EXISTS trips;
DROP TABLE IF EXISTS bookings;
DROP TABLE IF EXISTS booking_seats;
SET FOREIGN_KEY_CHECKS = 1;
"

# 6. 导入数据到 MySQL
echo -e "${YELLOW}📥 导入数据到 MySQL...${NC}"
mysql -h"$MYSQL_HOST" -u"$MYSQL_USER" -p"$MYSQL_PASS" "$MYSQL_DB" < "$BACKUP_DIR/mysql_import.sql"

# 7. 验证数据迁移
echo -e "${YELLOW}🔍 验证数据迁移...${NC}"

# 检查表数量
SQLITE_TABLES=$(sqlite3 "$SQLITE_DB" ".tables" | wc -w)
MYSQL_TABLES=$(mysql -h"$MYSQL_HOST" -u"$MYSQL_USER" -p"$MYSQL_PASS" "$MYSQL_DB" -e "SHOW TABLES;" | wc -l)
MYSQL_TABLES=$((MYSQL_TABLES - 1))  # 减去标题行

echo -e "SQLite 表数量: ${GREEN}$SQLITE_TABLES${NC}"
echo -e "MySQL 表数量: ${GREEN}$MYSQL_TABLES${NC}"

# 检查关键表的记录数
echo -e "${YELLOW}📊 检查关键表记录数...${NC}"

check_table_count() {
    local table=$1
    local sqlite_count=$(sqlite3 "$SQLITE_DB" "SELECT COUNT(*) FROM $table;" 2>/dev/null || echo "0")
    local mysql_count=$(mysql -h"$MYSQL_HOST" -u"$MYSQL_USER" -p"$MYSQL_PASS" "$MYSQL_DB" -e "SELECT COUNT(*) FROM $table;" 2>/dev/null | tail -n 1 || echo "0")
    
    echo -e "$table: SQLite=${GREEN}$sqlite_count${NC}, MySQL=${GREEN}$mysql_count${NC}"
    
    if [ "$sqlite_count" != "$mysql_count" ]; then
        echo -e "${YELLOW}⚠️ $table 表记录数不匹配${NC}"
    fi
}

# 检查主要表
check_table_count "users"
check_table_count "pages"
check_table_count "frontends"
check_table_count "general_settings"
check_table_count "admins"

# 8. 更新 Laravel 配置
echo -e "${YELLOW}⚙️ 更新 Laravel 配置...${NC}"
if [ -f "core/.env" ]; then
    # 备份原配置
    cp core/.env "$BACKUP_DIR/.env.backup"
    
    # 更新数据库配置
    sed -i "s/DB_CONNECTION=.*/DB_CONNECTION=mysql/" core/.env
    sed -i "s/DB_HOST=.*/DB_HOST=$MYSQL_HOST/" core/.env
    sed -i "s/DB_DATABASE=.*/DB_DATABASE=$MYSQL_DB/" core/.env
    sed -i "s/DB_USERNAME=.*/DB_USERNAME=$MYSQL_USER/" core/.env
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$MYSQL_PASS/" core/.env
    
    echo -e "${GREEN}✅ Laravel 配置已更新${NC}"
fi

# 9. 清除 Laravel 缓存
echo -e "${YELLOW}🧹 清除 Laravel 缓存...${NC}"
cd core
php artisan config:clear
php artisan cache:clear
php artisan config:cache

# 10. 测试数据库连接
echo -e "${YELLOW}🔍 测试数据库连接...${NC}"
if php artisan tinker --execute="echo 'Database connection test: '; var_dump(\Illuminate\Support\Facades\DB::connection()->getPdo());" &>/dev/null; then
    echo -e "${GREEN}✅ 数据库连接成功${NC}"
else
    echo -e "${RED}❌ 数据库连接失败${NC}"
    echo -e "${YELLOW}请检查 .env 配置${NC}"
fi

echo -e "${GREEN}✅ 数据库迁移完成!${NC}"
echo -e "${BLUE}📁 备份文件保存在: $BACKUP_DIR${NC}"
echo -e "${YELLOW}📝 迁移摘要:${NC}"
echo -e "- SQLite 数据已导出到: $BACKUP_DIR/sqlite_dump.sql"
echo -e "- MySQL 导入脚本: $BACKUP_DIR/mysql_import.sql"
echo -e "- 原 .env 配置备份: $BACKUP_DIR/.env.backup"
echo -e "- Laravel 配置已更新为使用 MySQL"

echo -e "${GREEN}🎉 迁移成功完成!${NC}"
