#!/bin/bash

# ViserBus 简化安装脚本
# 适用于快速部署

echo "🚀 ViserBus 简化安装脚本"
echo "========================"

# 进入项目目录
cd ~/superiortour.com.sg/core

echo ""
echo "📋 Step 1: 基础设置"

# 创建 .env 文件
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ .env 文件已创建"
fi

# 生成应用密钥
php artisan key:generate --force
echo "✅ 应用密钥已生成"

# 创建数据库文件
touch database/database.sqlite
chmod 664 database/database.sqlite
echo "✅ SQLite 数据库文件已创建"

echo ""
echo "📋 Step 2: 直接创建数据库表"

# 使用 SQLite 直接创建表
sqlite3 database/database.sqlite << 'EOF'
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    firstname TEXT,
    lastname TEXT,
    username TEXT UNIQUE,
    email TEXT UNIQUE,
    country_code TEXT,
    mobile TEXT,
    ref_by INTEGER DEFAULT 0,
    balance REAL DEFAULT 0,
    password TEXT,
    country_name TEXT,
    city TEXT,
    state TEXT,
    zip TEXT,
    address TEXT,
    status INTEGER DEFAULT 1,
    kyc_data TEXT,
    kv INTEGER DEFAULT 0,
    ev INTEGER DEFAULT 0,
    sv INTEGER DEFAULT 0,
    profile_complete INTEGER DEFAULT 0,
    ver_code TEXT,
    ver_code_send_at TEXT,
    ts INTEGER DEFAULT 0,
    tv INTEGER DEFAULT 1,
    tsc TEXT,
    ban_reason TEXT,
    remember_token TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 创建管理员表
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    email TEXT,
    username TEXT,
    email_verified_at TEXT,
    image TEXT,
    password TEXT,
    remember_token TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 创建通用设置表
CREATE TABLE IF NOT EXISTS general_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    site_name TEXT DEFAULT 'ViserBus',
    cur_text TEXT DEFAULT 'USD',
    cur_sym TEXT DEFAULT '$',
    email_from TEXT DEFAULT '<EMAIL>',
    email_from_name TEXT DEFAULT 'ViserBus',
    email_template TEXT,
    sms_template TEXT,
    sms_from TEXT,
    push_title TEXT,
    push_template TEXT,
    base_color TEXT DEFAULT '6777ef',
    mail_config TEXT,
    sms_config TEXT,
    firebase_config TEXT,
    global_shortcodes TEXT,
    ev INTEGER DEFAULT 1,
    en INTEGER DEFAULT 1,
    sv INTEGER DEFAULT 0,
    sn INTEGER DEFAULT 0,
    pn INTEGER DEFAULT 1,
    force_ssl INTEGER DEFAULT 0,
    maintenance_mode INTEGER DEFAULT 0,
    secure_password INTEGER DEFAULT 0,
    agree INTEGER DEFAULT 1,
    multi_language INTEGER DEFAULT 1,
    registration INTEGER DEFAULT 1,
    active_template TEXT DEFAULT 'basic',
    socialite_credentials TEXT,
    last_cron TEXT,
    available_version TEXT,
    system_customized INTEGER DEFAULT 0,
    paginate_number INTEGER DEFAULT 20,
    currency_format INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 创建前端内容表
CREATE TABLE IF NOT EXISTS frontends (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data_keys TEXT,
    data_values TEXT,
    seo_content TEXT,
    tempname TEXT,
    slug TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 创建页面表
CREATE TABLE IF NOT EXISTS pages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    slug TEXT,
    tempname TEXT,
    data_values TEXT,
    is_default INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 创建位置表
CREATE TABLE IF NOT EXISTS locations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    slug TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 创建缓存表
CREATE TABLE IF NOT EXISTS cache (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    expiration INTEGER NOT NULL
);

-- 创建作业表
CREATE TABLE IF NOT EXISTS jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    queue TEXT NOT NULL,
    payload TEXT NOT NULL,
    attempts INTEGER NOT NULL,
    reserved_at INTEGER,
    available_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL
);

-- 创建会话表
CREATE TABLE IF NOT EXISTS sessions (
    id TEXT PRIMARY KEY,
    user_id INTEGER,
    ip_address TEXT,
    user_agent TEXT,
    payload TEXT NOT NULL,
    last_activity INTEGER NOT NULL
);

-- 创建迁移表
CREATE TABLE IF NOT EXISTS migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    migration TEXT NOT NULL,
    batch INTEGER NOT NULL
);
EOF

echo "✅ 数据库表结构已创建"

echo ""
echo "📋 Step 3: 插入默认数据"

# 插入默认数据
sqlite3 database/database.sqlite << 'EOF'
-- 插入默认管理员
INSERT OR IGNORE INTO admins (id, name, email, username, password) VALUES 
(1, 'Super Admin', '<EMAIL>', 'admin', '$2y$12$vc.c.pNxefhOjFzLFNMEW.16i/h1vQCigtZeTLDY12QlIlS0KTWbm');

-- 插入默认设置
INSERT OR IGNORE INTO general_settings (id, site_name, cur_text, cur_sym, email_from, email_from_name, base_color, ev, en, sv, sn, pn, active_template, paginate_number, currency_format) VALUES 
(1, 'Superior Tour', 'USD', '$', '<EMAIL>', 'Superior Tour', '6777ef', 1, 1, 0, 0, 1, 'basic', 20, 1);

-- 插入迁移记录
INSERT OR IGNORE INTO migrations (migration, batch) VALUES 
('0001_01_01_000000_create_users_table', 1),
('0001_01_01_000001_create_cache_table', 1),
('0001_01_01_000002_create_jobs_table', 1),
('2025_09_16_192245_create_locations_table', 1),
('2025_09_16_194000_add_slug_to_locations_table', 1);
EOF

echo "✅ 默认数据已插入"

echo ""
echo "📋 Step 4: 设置权限"

# 设置权限
chmod -R 775 storage bootstrap/cache
chmod 664 database/database.sqlite

echo "✅ 文件权限已设置"

echo ""
echo "📋 Step 5: 清理缓存"

# 清理和优化
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo "✅ 缓存已清理"

echo ""
echo "📋 Step 6: 验证安装"

# 验证数据库
echo "检查数据库表:"
sqlite3 database/database.sqlite "SELECT name FROM sqlite_master WHERE type='table';" | while read table; do
    count=$(sqlite3 database/database.sqlite "SELECT COUNT(*) FROM $table;")
    echo "  ✅ $table ($count 条记录)"
done

echo ""
echo "🎉 简化安装完成！"
echo ""
echo "默认管理员账户:"
echo "  用户名: admin"
echo "  密码: password"
echo ""
echo "请访问您的网站测试安装结果！"
