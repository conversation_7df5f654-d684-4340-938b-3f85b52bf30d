#!/bin/bash

# ViserBus 故障排除脚本
# 用于诊断和修复常见问题

echo "🔧 ViserBus 故障排除脚本"
echo "======================="

cd ~/superiortour.com.sg/core

echo ""
echo "📋 诊断系统状态"

# 检查基本文件
echo "1. 检查基本文件:"
if [ -f "artisan" ]; then
    echo "  ✅ artisan 文件存在"
else
    echo "  ❌ artisan 文件缺失"
fi

if [ -f ".env" ]; then
    echo "  ✅ .env 文件存在"
else
    echo "  ❌ .env 文件缺失"
fi

if [ -f "database/database.sqlite" ]; then
    echo "  ✅ SQLite 数据库文件存在"
    ls -la database/database.sqlite
else
    echo "  ❌ SQLite 数据库文件缺失"
fi

echo ""
echo "2. 检查目录权限:"
ls -la storage/ | head -5
ls -la bootstrap/cache/ | head -5

echo ""
echo "3. 检查数据库表:"
if [ -f "database/database.sqlite" ]; then
    echo "数据库中的表:"
    sqlite3 database/database.sqlite "SELECT name FROM sqlite_master WHERE type='table';" | while read table; do
        if [ ! -z "$table" ]; then
            count=$(sqlite3 database/database.sqlite "SELECT COUNT(*) FROM $table;" 2>/dev/null || echo "ERROR")
            echo "  - $table: $count 条记录"
        fi
    done
else
    echo "  ❌ 数据库文件不存在"
fi

echo ""
echo "4. 测试 PHP 和 Laravel:"
echo "PHP 版本: $(php -v | head -1)"
echo "Laravel 状态:"
if php artisan --version 2>/dev/null; then
    echo "  ✅ Laravel 正常"
else
    echo "  ❌ Laravel 有问题"
fi

echo ""
echo "5. 测试数据库连接:"
if php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'Database: OK'; } catch(Exception \$e) { echo 'Database Error: ' . \$e->getMessage(); }" 2>/dev/null; then
    echo "  ✅ 数据库连接正常"
else
    echo "  ❌ 数据库连接失败"
fi

echo ""
echo "📋 自动修复选项"
echo ""
echo "选择修复操作:"
echo "1. 重新创建 .env 文件"
echo "2. 重新生成应用密钥"
echo "3. 重新创建数据库文件"
echo "4. 重新创建所有数据库表"
echo "5. 修复文件权限"
echo "6. 清理所有缓存"
echo "7. 完全重置 (危险!)"
echo "8. 退出"
echo ""

read -p "请选择操作 (1-8): " choice

case $choice in
    1)
        echo "重新创建 .env 文件..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            echo "✅ .env 文件已重新创建"
        else
            echo "❌ .env.example 文件不存在"
        fi
        ;;
    2)
        echo "重新生成应用密钥..."
        php artisan key:generate --force
        echo "✅ 应用密钥已重新生成"
        ;;
    3)
        echo "重新创建数据库文件..."
        rm -f database/database.sqlite
        touch database/database.sqlite
        chmod 664 database/database.sqlite
        echo "✅ 数据库文件已重新创建"
        ;;
    4)
        echo "重新创建所有数据库表..."
        rm -f database/database.sqlite
        touch database/database.sqlite
        chmod 664 database/database.sqlite
        
        # 运行简化安装脚本的数据库部分
        sqlite3 database/database.sqlite << 'EOF'
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    firstname TEXT, lastname TEXT, username TEXT UNIQUE, email TEXT UNIQUE,
    country_code TEXT, mobile TEXT, ref_by INTEGER DEFAULT 0, balance REAL DEFAULT 0,
    password TEXT, country_name TEXT, city TEXT, state TEXT, zip TEXT, address TEXT,
    status INTEGER DEFAULT 1, kyc_data TEXT, kv INTEGER DEFAULT 0, ev INTEGER DEFAULT 0,
    sv INTEGER DEFAULT 0, profile_complete INTEGER DEFAULT 0, ver_code TEXT,
    ver_code_send_at TEXT, ts INTEGER DEFAULT 0, tv INTEGER DEFAULT 1, tsc TEXT,
    ban_reason TEXT, remember_token TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP, updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, email TEXT, username TEXT,
    email_verified_at TEXT, image TEXT, password TEXT, remember_token TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP, updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS general_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT, site_name TEXT DEFAULT 'Superior Tour',
    cur_text TEXT DEFAULT 'USD', cur_sym TEXT DEFAULT '$',
    email_from TEXT DEFAULT '<EMAIL>', email_from_name TEXT DEFAULT 'Superior Tour',
    email_template TEXT, sms_template TEXT, sms_from TEXT, push_title TEXT, push_template TEXT,
    base_color TEXT DEFAULT '6777ef', mail_config TEXT, sms_config TEXT, firebase_config TEXT,
    global_shortcodes TEXT, ev INTEGER DEFAULT 1, en INTEGER DEFAULT 1, sv INTEGER DEFAULT 0,
    sn INTEGER DEFAULT 0, pn INTEGER DEFAULT 1, force_ssl INTEGER DEFAULT 0,
    maintenance_mode INTEGER DEFAULT 0, secure_password INTEGER DEFAULT 0, agree INTEGER DEFAULT 1,
    multi_language INTEGER DEFAULT 1, registration INTEGER DEFAULT 1, active_template TEXT DEFAULT 'basic',
    socialite_credentials TEXT, last_cron TEXT, available_version TEXT, system_customized INTEGER DEFAULT 0,
    paginate_number INTEGER DEFAULT 20, currency_format INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP, updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS frontends (
    id INTEGER PRIMARY KEY AUTOINCREMENT, data_keys TEXT, data_values TEXT, seo_content TEXT,
    tempname TEXT, slug TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP, updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS pages (
    id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, slug TEXT, tempname TEXT, data_values TEXT,
    is_default INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP, updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS locations (
    id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT NOT NULL, slug TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP, updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS cache (
    key TEXT PRIMARY KEY, value TEXT NOT NULL, expiration INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT, queue TEXT NOT NULL, payload TEXT NOT NULL,
    attempts INTEGER NOT NULL, reserved_at INTEGER, available_at INTEGER NOT NULL, created_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS sessions (
    id TEXT PRIMARY KEY, user_id INTEGER, ip_address TEXT, user_agent TEXT,
    payload TEXT NOT NULL, last_activity INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT, migration TEXT NOT NULL, batch INTEGER NOT NULL
);

-- 插入默认数据
INSERT OR IGNORE INTO admins (id, name, email, username, password) VALUES 
(1, 'Super Admin', '<EMAIL>', 'admin', '$2y$12$vc.c.pNxefhOjFzLFNMEW.16i/h1vQCigtZeTLDY12QlIlS0KTWbm');

INSERT OR IGNORE INTO general_settings (id, site_name, cur_text, cur_sym, email_from, email_from_name, base_color, ev, en, sv, sn, pn, active_template, paginate_number, currency_format) VALUES 
(1, 'Superior Tour', 'USD', '$', '<EMAIL>', 'Superior Tour', '6777ef', 1, 1, 0, 0, 1, 'basic', 20, 1);

INSERT OR IGNORE INTO migrations (migration, batch) VALUES 
('0001_01_01_000000_create_users_table', 1),
('0001_01_01_000001_create_cache_table', 1),
('0001_01_01_000002_create_jobs_table', 1),
('2025_09_16_192245_create_locations_table', 1),
('2025_09_16_194000_add_slug_to_locations_table', 1);
EOF
        echo "✅ 数据库表已重新创建"
        ;;
    5)
        echo "修复文件权限..."
        chmod -R 775 storage
        chmod -R 775 bootstrap/cache
        chmod 664 database/database.sqlite
        echo "✅ 文件权限已修复"
        ;;
    6)
        echo "清理所有缓存..."
        php artisan config:clear
        php artisan cache:clear
        php artisan view:clear
        php artisan route:clear
        echo "✅ 缓存已清理"
        ;;
    7)
        echo "⚠️  警告: 这将删除所有数据!"
        read -p "确定要继续吗? (y/N): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            echo "执行完全重置..."
            rm -f database/database.sqlite
            rm -f .env
            ./simple_install.sh
            echo "✅ 完全重置完成"
        else
            echo "操作已取消"
        fi
        ;;
    8)
        echo "退出故障排除"
        exit 0
        ;;
    *)
        echo "无效选择"
        ;;
esac

echo ""
echo "🎉 故障排除完成!"
echo "请重新测试您的网站"
